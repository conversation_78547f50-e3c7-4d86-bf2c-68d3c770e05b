<?php
/**
 * قالب إضافة مستخدم جديد
 */
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3"><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php?page=dashboard">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=users">المستخدمين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إضافة مستخدم</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>بيانات المستخدم الجديد</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    
                    <form method="post" action="index.php?page=users&action=add">
                        <div class="mb-3">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الصلاحية</option>
                                <option value="admin" <?php echo (isset($_POST['role']) && $_POST['role'] === 'admin') ? 'selected' : ''; ?>>مدير النظام</option>
                                <option value="camp_manager" <?php echo (isset($_POST['role']) && $_POST['role'] === 'camp_manager') ? 'selected' : ''; ?>>مدير موقع</option>
                                <option value="accountant" <?php echo (isset($_POST['role']) && $_POST['role'] === 'accountant') ? 'selected' : ''; ?>>محاسب</option>
                                <option value="worker" <?php echo (isset($_POST['role']) && $_POST['role'] === 'worker') ? 'selected' : ''; ?>>عامل</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="camp_id" class="form-label">الموقع</label>
                            <select class="form-select" id="camp_id" name="camp_id">
                                <option value="">اختر الموقع</option>
                                <?php foreach ($camps as $camp): ?>
                                <option value="<?php echo $camp['id']; ?>" <?php echo (isset($_POST['camp_id']) && $_POST['camp_id'] == $camp['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($camp['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" name="add_user" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ
                            </button>
                            <a href="index.php?page=users" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>