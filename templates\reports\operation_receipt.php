<?php
/**
 * قالب إيصال العملية
 */
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال عملية #<?php echo $operation['id']; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }
        .receipt {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
        }
        .receipt-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-subtitle {
            font-size: 16px;
            color: #666;
        }
        .receipt-info {
            margin-bottom: 20px;
        }
        .receipt-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .receipt-info table th {
            text-align: right;
            padding: 8px;
            width: 40%;
        }
        .receipt-info table td {
            padding: 8px;
        }
        .receipt-details {
            margin-bottom: 20px;
        }
        .receipt-details table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #ddd;
        }
        .receipt-details table th,
        .receipt-details table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .receipt-details table th {
            background-color: #f5f5f5;
        }
        .receipt-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .operation-type {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .operation-type.entry {
            background-color: #28a745;
        }
        .operation-type.exit {
            background-color: #dc3545;
        }
        .signature {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 45%;
            text-align: center;
        }
        .signature-line {
            margin-top: 50px;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        @media print {
            body {
                padding: 0;
            }
            .receipt {
                border: none;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="receipt-header">
            <div class="receipt-title"><?php echo $operation['camp_name']; ?></div>
            <div class="receipt-subtitle">إيصال عملية #<?php echo $operation['id']; ?></div>
        </div>
        
        <div class="receipt-info">
            <table>
                <tr>
                    <th>التاريخ والوقت:</th>
                    <td><?php echo date('Y-m-d H:i:s', strtotime($operation['created_at'])); ?></td>
                </tr>
                <tr>
                    <th>اسم السائق:</th>
                    <td><?php echo $operation['driver_name']; ?></td>
                </tr>
                <tr>
                    <th>رقم السيارة:</th>
                    <td><?php echo $operation['vehicle_number']; ?></td>
                </tr>
                <tr>
                    <th>نوع العملية:</th>
                    <td>
                        <span class="operation-type <?php echo $operation['operation_type']; ?>">
                            <?php echo $operation['operation_type'] == 'entry' ? 'دخول' : 'خروج'; ?>
                        </span>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="receipt-details">
            <table>
                <thead>
                    <tr>
                        <th>المادة</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><?php echo $operation['material_name']; ?></td>
                        <td><?php echo $operation['quantity']; ?></td>
                        <td><?php echo number_format($operation['cost'] / $operation['quantity']); ?> د.ع</td>
                        <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3">الإجمالي</th>
                        <th><?php echo number_format($operation['cost']); ?> د.ع</th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <?php if (!empty($operation['notes'])): ?>
        <div class="receipt-info">
            <table>
                <tr>
                    <th>ملاحظات:</th>
                    <td><?php echo $operation['notes']; ?></td>
                </tr>
            </table>
        </div>
        <?php endif; ?>
        
        <div class="signature">
            <div class="signature-box">
                <div class="signature-line">توقيع السائق</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">توقيع المسؤول: <?php echo $operation['user_name']; ?></div>
            </div>
        </div>
        
        <div class="receipt-footer">
            <p>هذا الإيصال تم إنشاؤه بواسطة نظام إدارة عمليات دخول وخروج سيارات الحمل</p>
            <p>تاريخ الطباعة: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
    
    <div class="text-center mt-4 no-print">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print me-2"></i>طباعة الإيصال
        </button>
        <a href="index.php?page=operations" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة إلى قائمة العمليات
        </a>
    </div>
</body>
</html>