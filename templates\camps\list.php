<?php
/**
 * قالب عرض قائمة المواقع
 */
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-map-marker-alt me-2"></i>إدارة المواقع</h2>
        <div>
            <a href="index.php?page=camps&action=add" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>إضافة موقع جديد
            </a>
        </div>
    </div>
    
    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $_GET['success']; ?>
    </div>
    <?php endif; ?>
    
    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?php echo $_GET['error']; ?>
    </div>
    <?php endif; ?>
    
    <!-- جدول المواقع -->
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة المواقع</h5>
            <span class="badge bg-primary"><?php echo count($camps); ?> موقع</span>
        </div>
        <div class="card-body">
            <?php if (empty($camps)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>لا توجد مواقع مسجلة
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم الموقع</th>
                            <th>الشركة</th>
                            <th>الموقع</th>
                            <th>المسؤول</th>
                            <th>رقم الهاتف</th>
                            <th>عدد السائقين</th>
                            <th>عدد العمليات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($camps as $index => $camp): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($camp['name']); ?></td>
                            <td><?php echo htmlspecialchars($camp['company_name']); ?></td>
                            <td><?php echo htmlspecialchars($camp['location'] ?: '-'); ?></td>
                            <td><?php echo htmlspecialchars($camp['manager_name'] ?: '-'); ?></td>
                            <td><?php echo htmlspecialchars($camp['phone'] ?: '-'); ?></td>
                            <td><span class="badge bg-info"><?php echo $camp['driver_count']; ?></span></td>
                            <td><span class="badge bg-primary"><?php echo $camp['operation_count']; ?></span></td>
                            <td>
                                <a href="index.php?page=camps&action=view&id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="index.php?page=camps&action=edit&id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="javascript:void(0);" onclick="confirmDelete(<?php echo $camp['id']; ?>, '<?php echo htmlspecialchars($camp['name']); ?>')" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- سكريبت تأكيد الحذف -->
<script>
function confirmDelete(id, name) {
    if (confirm('هل أنت متأكد من حذف الموقع: ' + name + '؟')) {
        window.location.href = 'index.php?page=camps&action=delete&id=' + id;
    }
}
</script>