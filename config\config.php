<?php
/**
 * ملف الإعدادات العامة للنظام
 */

// تضمين ملف إعدادات قاعدة البيانات
require_once 'database.php';

// إعدادات عامة للنظام
define('SITE_NAME', 'نظام إدارة سيارات الحمل');
define('SITE_URL', 'http://localhost/car_management');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الجلسة
session_start();

/**
 * دالة للتحقق من تسجيل دخول المستخدم
 * إذا لم يكن المستخدم مسجل الدخول، يتم توجيهه إلى صفحة تسجيل الدخول
 */
function requireLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

/**
 * دالة للتحقق من صلاحيات المستخدم
 * @param string $permission الصلاحية المطلوبة
 * @return bool
 */
function hasPermission($permission) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // إذا كان المستخدم مدير النظام، فلديه جميع الصلاحيات
    if ($_SESSION['user_role'] === 'admin') {
        return true;
    }
    
    // التحقق من الصلاحيات حسب دور المستخدم
    switch ($_SESSION['user_role']) {
        case 'camp_manager':
            $campManagerPermissions = [
                'manage_drivers',
                'view_reports',
                'manage_operations'
            ];
            return in_array($permission, $campManagerPermissions);
            
        case 'accountant':
            $accountantPermissions = [
                'view_reports',
                'view_operations'
            ];
            return in_array($permission, $accountantPermissions);
            
        case 'worker':
            $workerPermissions = [
                'add_operations'
            ];
            return in_array($permission, $workerPermissions);
            
        default:
            return false;
    }
}

/**
 * دالة للتحقق من وجود صلاحية معينة وتوجيه المستخدم إلى الصفحة الرئيسية إذا لم تكن لديه الصلاحية
 * @param string $permission الصلاحية المطلوبة
 */
function requirePermission($permission) {
    if (!hasPermission($permission)) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        header('Location: index.php');
        exit;
    }
}

/**
 * دالة لتنظيف المدخلات
 * @param string $input المدخل المراد تنظيفه
 * @return string
 */
function sanitizeInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * دالة لعرض رسالة نجاح
 * @param string $message نص الرسالة
 * @return string
 */
function showSuccess($message) {
    return '<div class="alert alert-success">' . $message . '</div>';
}

/**
 * دالة لعرض رسالة خطأ
 * @param string $message نص الرسالة
 * @return string
 */
function showError($message) {
    return '<div class="alert alert-danger">' . $message . '</div>';
}

/**
 * دالة لتوليد رمز QR
 * @param string $data البيانات المراد تحويلها إلى رمز QR
 * @param int $size حجم الرمز
 * @return string رابط صورة رمز QR
 */
function generateQRCode($data, $size = 200) {
    // استخدام QRServer API لتوليد رمز QR بدلاً من Google Chart API التي تم إيقافها
    $url = 'https://api.qrserver.com/v1/create-qr-code/?size=' . $size . 'x' . $size . '&data=' . urlencode($data) . '&ecc=M';
    return $url;
}

/**
 * دالة لتحويل التاريخ إلى الصيغة العربية
 * @param string $date التاريخ بصيغة Y-m-d
 * @return string
 */
function formatDate($date) {
    if (empty($date)) return '';
    
    $timestamp = strtotime($date);
    $months = [
        'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp) - 1];
    $year = date('Y', $timestamp);
    
    return $day . ' ' . $month . ' ' . $year;
}

/**
 * دالة لتنسيق المبلغ المالي
 * @param float $amount المبلغ
 * @param string $currency العملة (افتراضياً: دينار عراقي)
 * @return string
 */
function formatCurrency($amount, $currency = 'دينار عراقي') {
    return number_format($amount, 0, '.', ',') . ' ' . $currency;
}
?>