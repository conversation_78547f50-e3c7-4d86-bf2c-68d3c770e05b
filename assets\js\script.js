/**
 * ملف JavaScript مخصص لنظام إدارة سيارات الحمل
 * تصميم حديث مشابه لأنظمة Google
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
  // تهيئة القائمة الجانبية للجوال
  initializeMobileSidebar();

  // تهيئة التنبيهات التلقائية
  initializeAutoAlerts();

  // تهيئة التحقق من النماذج
  initializeFormValidation();

  // تهيئة الجداول التفاعلية
  initializeInteractiveTables();

  // تهيئة البحث المباشر
  initializeLiveSearch();

  // تهيئة التحديث التلقائي للوقت
  initializeTimeUpdates();

  // تهيئة الرسوم البيانية
  initializeCharts();

  // تهيئة التأثيرات البصرية
  initializeVisualEffects();
}

/**
 * تهيئة القائمة الجانبية للجوال
 */
function initializeMobileSidebar() {
  // إنشاء زر القائمة للجوال
  const navbar = document.querySelector(".top-navbar");
  if (navbar && window.innerWidth <= 768) {
    const menuButton = document.createElement("button");
    menuButton.className = "btn btn-primary d-md-none";
    menuButton.innerHTML = '<i class="material-icons">menu</i>';
    menuButton.onclick = toggleMobileSidebar;

    const navbarContent = navbar.querySelector(".container-fluid");
    if (navbarContent) {
      navbarContent.insertBefore(menuButton, navbarContent.firstChild);
    }
  }

  // إضافة overlay للقائمة الجانبية
  const overlay = document.createElement("div");
  overlay.className = "sidebar-overlay d-md-none";
  overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1040;
        display: none;
        transition: opacity 0.3s ease;
    `;
  overlay.onclick = closeMobileSidebar;
  document.body.appendChild(overlay);
}

/**
 * تبديل القائمة الجانبية للجوال
 */
function toggleMobileSidebar() {
  const sidebar = document.querySelector(".sidebar");
  const overlay = document.querySelector(".sidebar-overlay");

  if (sidebar && overlay) {
    sidebar.classList.toggle("show");
    overlay.style.display = sidebar.classList.contains("show")
      ? "block"
      : "none";

    // منع التمرير عند فتح القائمة
    document.body.style.overflow = sidebar.classList.contains("show")
      ? "hidden"
      : "";
  }
}

/**
 * إغلاق القائمة الجانبية للجوال
 */
function closeMobileSidebar() {
  const sidebar = document.querySelector(".sidebar");
  const overlay = document.querySelector(".sidebar-overlay");

  if (sidebar && overlay) {
    sidebar.classList.remove("show");
    overlay.style.display = "none";
    document.body.style.overflow = "";
  }
}

/**
 * تهيئة التنبيهات التلقائية
 */
function initializeAutoAlerts() {
  // إخفاء التنبيهات تلقائياً بعد 5 ثوان
  const alerts = document.querySelectorAll(".alert");
  alerts.forEach((alert) => {
    if (!alert.classList.contains("alert-permanent")) {
      setTimeout(() => {
        fadeOut(alert);
      }, 5000);
    }
  });
}

/**
 * تهيئة التحقق من النماذج
 */
function initializeFormValidation() {
  const forms = document.querySelectorAll("form");
  forms.forEach((form) => {
    form.addEventListener("submit", function (e) {
      if (!validateForm(this)) {
        e.preventDefault();
        showAlert("يرجى التحقق من البيانات المدخلة", "warning");
      }
    });

    // التحقق المباشر من الحقول
    const inputs = form.querySelectorAll("input, select, textarea");
    inputs.forEach((input) => {
      input.addEventListener("blur", function () {
        validateField(this);
      });

      input.addEventListener("input", function () {
        clearFieldError(this);
      });
    });
  });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
  let isValid = true;
  const requiredFields = form.querySelectorAll("[required]");

  requiredFields.forEach((field) => {
    if (!validateField(field)) {
      isValid = false;
    }
  });

  return isValid;
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
  const value = field.value.trim();
  let isValid = true;
  let errorMessage = "";

  // التحقق من الحقول المطلوبة
  if (field.hasAttribute("required") && !value) {
    isValid = false;
    errorMessage = "هذا الحقل مطلوب";
  }

  // التحقق من البريد الإلكتروني
  if (field.type === "email" && value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      isValid = false;
      errorMessage = "يرجى إدخال بريد إلكتروني صحيح";
    }
  }

  // التحقق من رقم الهاتف
  if (field.type === "tel" && value) {
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(value)) {
      isValid = false;
      errorMessage = "يرجى إدخال رقم هاتف صحيح";
    }
  }

  // التحقق من الأرقام
  if (field.type === "number" && value) {
    if (isNaN(value) || value < 0) {
      isValid = false;
      errorMessage = "يرجى إدخال رقم صحيح";
    }
  }

  // عرض أو إخفاء رسالة الخطأ
  if (!isValid) {
    showFieldError(field, errorMessage);
  } else {
    clearFieldError(field);
  }

  return isValid;
}

/**
 * عرض خطأ في حقل
 */
function showFieldError(field, message) {
  clearFieldError(field);

  field.classList.add("is-invalid");

  const errorDiv = document.createElement("div");
  errorDiv.className = "invalid-feedback";
  errorDiv.textContent = message;

  field.parentNode.appendChild(errorDiv);
}

/**
 * إزالة خطأ من حقل
 */
function clearFieldError(field) {
  field.classList.remove("is-invalid");

  const errorDiv = field.parentNode.querySelector(".invalid-feedback");
  if (errorDiv) {
    errorDiv.remove();
  }
}

/**
 * تهيئة الجداول التفاعلية
 */
function initializeInteractiveTables() {
  const tables = document.querySelectorAll(".table");
  tables.forEach((table) => {
    // إضافة تأثير hover للصفوف
    const rows = table.querySelectorAll("tbody tr");
    rows.forEach((row) => {
      row.addEventListener("mouseenter", function () {
        this.style.transform = "scale(1.01)";
        this.style.transition = "all 0.2s ease";
      });

      row.addEventListener("mouseleave", function () {
        this.style.transform = "scale(1)";
      });
    });

    // إضافة ترقيم تلقائي للصفوف
    addRowNumbers(table);
  });
}

/**
 * إضافة ترقيم للجدول
 */
function addRowNumbers(table) {
  const tbody = table.querySelector("tbody");
  if (!tbody) return;

  const rows = tbody.querySelectorAll("tr");
  rows.forEach((row, index) => {
    const firstCell = row.querySelector("td");
    if (firstCell && !firstCell.classList.contains("row-number")) {
      const numberCell = document.createElement("td");
      numberCell.className = "row-number text-muted";
      numberCell.textContent = index + 1;
      row.insertBefore(numberCell, firstCell);
    }
  });

  // إضافة عنوان للعمود
  const thead = table.querySelector("thead tr");
  if (thead) {
    const firstHeader = thead.querySelector("th");
    if (firstHeader && !firstHeader.classList.contains("row-number-header")) {
      const numberHeader = document.createElement("th");
      numberHeader.className = "row-number-header";
      numberHeader.textContent = "#";
      numberHeader.style.width = "50px";
      thead.insertBefore(numberHeader, firstHeader);
    }
  }
}

/**
 * تهيئة البحث المباشر
 */
function initializeLiveSearch() {
  const searchInputs = document.querySelectorAll("[data-search-target]");
  searchInputs.forEach((input) => {
    input.addEventListener("input", function () {
      const target = document.querySelector(this.dataset.searchTarget);
      if (target) {
        performLiveSearch(this.value, target);
      }
    });
  });
}

/**
 * تنفيذ البحث المباشر
 */
function performLiveSearch(query, target) {
  const rows = target.querySelectorAll("tbody tr");
  const searchTerm = query.toLowerCase().trim();

  rows.forEach((row) => {
    const text = row.textContent.toLowerCase();
    const shouldShow = !searchTerm || text.includes(searchTerm);

    row.style.display = shouldShow ? "" : "none";

    if (shouldShow && searchTerm) {
      // تمييز النص المطابق
      highlightSearchTerm(row, searchTerm);
    } else {
      // إزالة التمييز
      removeHighlight(row);
    }
  });

  // عرض رسالة عدم وجود نتائج
  showNoResultsMessage(target, query);
}

/**
 * تمييز النص المطابق للبحث
 */
function highlightSearchTerm(row, term) {
  const cells = row.querySelectorAll("td");
  cells.forEach((cell) => {
    const originalText = cell.textContent;
    const regex = new RegExp(`(${term})`, "gi");
    const highlightedText = originalText.replace(regex, "<mark>$1</mark>");

    if (highlightedText !== originalText) {
      cell.innerHTML = highlightedText;
    }
  });
}

/**
 * إزالة تمييز النص
 */
function removeHighlight(row) {
  const marks = row.querySelectorAll("mark");
  marks.forEach((mark) => {
    mark.outerHTML = mark.textContent;
  });
}

/**
 * عرض رسالة عدم وجود نتائج
 */
function showNoResultsMessage(target, query) {
  const tbody = target.querySelector("tbody");
  const visibleRows = tbody.querySelectorAll('tr[style=""], tr:not([style])');

  // إزالة رسالة سابقة
  const existingMessage = tbody.querySelector(".no-results-message");
  if (existingMessage) {
    existingMessage.remove();
  }

  if (visibleRows.length === 0 && query.trim()) {
    const messageRow = document.createElement("tr");
    messageRow.className = "no-results-message";
    messageRow.innerHTML = `
            <td colspan="100%" class="text-center text-muted py-4">
                <i class="material-icons" style="font-size: 48px; opacity: 0.5;">search_off</i>
                <div class="mt-2">لا توجد نتائج مطابقة للبحث "${query}"</div>
            </td>
        `;
    tbody.appendChild(messageRow);
  }
}

/**
 * تهيئة التحديث التلقائي للوقت
 */
function initializeTimeUpdates() {
  updateTimeElements();
  setInterval(updateTimeElements, 60000); // تحديث كل دقيقة
}

/**
 * تحديث عناصر الوقت
 */
function updateTimeElements() {
  const timeElements = document.querySelectorAll("[data-time]");
  timeElements.forEach((element) => {
    const timestamp = element.dataset.time;
    if (timestamp) {
      element.textContent = formatRelativeTime(new Date(timestamp));
    }
  });
}

/**
 * تنسيق الوقت النسبي
 */
function formatRelativeTime(date) {
  const now = new Date();
  const diff = now - date;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (minutes < 1) return "الآن";
  if (minutes < 60) return `منذ ${minutes} دقيقة`;
  if (hours < 24) return `منذ ${hours} ساعة`;
  if (days < 7) return `منذ ${days} يوم`;

  return date.toLocaleDateString("ar-SA");
}

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts() {
  // تحسين الرسوم البيانية الموجودة
  if (typeof Chart !== "undefined") {
    Chart.defaults.font.family = "Cairo";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = "#424242";

    // إعدادات مخصصة للرسوم البيانية
    Chart.defaults.plugins.legend.labels.usePointStyle = true;
    Chart.defaults.plugins.legend.labels.padding = 20;
  }
}

/**
 * تهيئة التأثيرات البصرية
 */
function initializeVisualEffects() {
  // تأثير الظهور التدريجي للبطاقات
  const cards = document.querySelectorAll(".card");
  cards.forEach((card, index) => {
    card.style.opacity = "0";
    card.style.transform = "translateY(20px)";

    setTimeout(() => {
      card.style.transition = "all 0.5s ease";
      card.style.opacity = "1";
      card.style.transform = "translateY(0)";
    }, index * 100);
  });

  // تأثير النقر على الأزرار
  const buttons = document.querySelectorAll(".btn");
  buttons.forEach((button) => {
    button.addEventListener("click", function (e) {
      createRippleEffect(e, this);
    });
  });
}

/**
 * إنشاء تأثير الموجة عند النقر
 */
function createRippleEffect(event, element) {
  const ripple = document.createElement("span");
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;

  ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;

  element.style.position = "relative";
  element.style.overflow = "hidden";
  element.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

/**
 * إضافة CSS للتأثيرات
 */
const style = document.createElement("style");
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }
    
    .slide-in-right {
        animation: slideInRight 0.5s ease-in-out;
    }
    
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .bounce-in {
        animation: bounceIn 0.6s ease-in-out;
    }
    
    @keyframes bounceIn {
        0% {
            transform: scale(0.3);
            opacity: 0;
        }
        50% {
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

/**
 * دوال مساعدة عامة
 */

/**
 * عرض تنبيه
 */
function showAlert(message, type = "info", duration = 5000) {
  const alertDiv = document.createElement("div");
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

  // إضافة التنبيه إلى أعلى الصفحة
  const container = document.querySelector(".content") || document.body;
  container.insertBefore(alertDiv, container.firstChild);

  // إخفاء التنبيه تلقائياً
  if (duration > 0) {
    setTimeout(() => {
      fadeOut(alertDiv);
    }, duration);
  }
}

/**
 * تأثير الاختفاء التدريجي
 */
function fadeOut(element) {
  element.style.transition = "opacity 0.5s ease";
  element.style.opacity = "0";

  setTimeout(() => {
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    }
  }, 500);
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = "هل أنت متأكد من الحذف؟") {
  return new Promise((resolve) => {
    const modal = document.createElement("div");
    modal.className = "modal fade";
    modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تأكيد الحذف</h5>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger confirm-delete">حذف</button>
                    </div>
                </div>
            </div>
        `;

    document.body.appendChild(modal);

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    modal.querySelector(".confirm-delete").onclick = () => {
      bootstrapModal.hide();
      resolve(true);
    };

    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
      resolve(false);
    });
  });
}

/**
 * تحميل البيانات بشكل غير متزامن
 */
async function loadData(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error loading data:", error);
    showAlert("حدث خطأ في تحميل البيانات", "error");
    return null;
  }
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, locale = "ar-SA") {
  return new Intl.NumberFormat(locale).format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = "IQD", locale = "ar-IQ") {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currency,
  }).format(amount);
}

/**
 * نسخ النص إلى الحافظة
 */
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    showAlert("تم نسخ النص بنجاح", "success", 2000);
  } catch (error) {
    console.error("Failed to copy text:", error);
    showAlert("فشل في نسخ النص", "error");
  }
}

/**
 * تحديث الصفحة بسلاسة
 */
function smoothRefresh() {
  document.body.style.opacity = "0";
  document.body.style.transition = "opacity 0.3s ease";

  setTimeout(() => {
    window.location.reload();
  }, 300);
}

// تصدير الدوال للاستخدام العام
window.CarManagementSystem = {
  showAlert,
  confirmDelete,
  loadData,
  formatNumber,
  formatCurrency,
  copyToClipboard,
  smoothRefresh,
  toggleMobileSidebar,
  closeMobileSidebar,
};
