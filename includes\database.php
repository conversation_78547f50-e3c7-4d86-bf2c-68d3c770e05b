<?php
/**
 * ملف الاتصال بقاعدة البيانات
 */

// معلومات الاتصال بقاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'car_management');

// إنشاء اتصال بقاعدة البيانات
function connectDB() {
    static $conn;
    
    if ($conn === null) {
        try {
            $conn = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    return $conn;
}

/**
 * تنفيذ استعلام وإرجاع صف واحد
 * @param string $query استعلام SQL
 * @param array $params معلمات الاستعلام
 * @return array|false صف واحد أو false إذا لم يتم العثور على نتائج
 */
function fetchRow($query, $params = []) {
    try {
        $stmt = connectDB()->prepare($query);
        $stmt->execute($params);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('خطأ في الاستعلام: ' . $e->getMessage());
        return false;
    }
}

/**
 * تنفيذ استعلام وإرجاع جميع الصفوف
 * @param string $query استعلام SQL
 * @param array $params معلمات الاستعلام
 * @return array مصفوفة من الصفوف
 */
function fetchAll($query, $params = []) {
    try {
        $stmt = connectDB()->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('خطأ في الاستعلام: ' . $e->getMessage());
        return [];
    }
}

/**
 * إدخال بيانات في جدول
 * @param string $table اسم الجدول
 * @param array $data البيانات المراد إدخالها
 * @return int|false معرف السجل المدخل أو false في حالة الفشل
 */
function insertData($table, $data) {
    try {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = connectDB()->prepare($query);
        $stmt->execute(array_values($data));
        
        return connectDB()->lastInsertId();
    } catch (PDOException $e) {
        error_log('خطأ في إدخال البيانات: ' . $e->getMessage());
        return false;
    }
}

/**
 * تحديث بيانات في جدول
 * @param string $table اسم الجدول
 * @param array $data البيانات المراد تحديثها
 * @param string $where شرط التحديث
 * @param array $params معلمات الشرط
 * @return bool نجاح أو فشل العملية
 */
function updateData($table, $data, $where, $params = []) {
    try {
        $setClauses = [];
        $updateParams = [];
        
        foreach ($data as $column => $value) {
            $setClauses[] = "{$column} = ?";
            $updateParams[] = $value;
        }
        
        $setClause = implode(', ', $setClauses);
        $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $stmt = connectDB()->prepare($query);
        $stmt->execute(array_merge($updateParams, $params));
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log('خطأ في تحديث البيانات: ' . $e->getMessage());
        return false;
    }
}

/**
 * حذف بيانات من جدول
 * @param string $table اسم الجدول
 * @param string $where شرط الحذف
 * @param array $params معلمات الشرط
 * @return bool نجاح أو فشل العملية
 */
function deleteData($table, $where, $params = []) {
    try {
        $query = "DELETE FROM {$table} WHERE {$where}";
        $stmt = connectDB()->prepare($query);
        $stmt->execute($params);
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log('خطأ في حذف البيانات: ' . $e->getMessage());
        return false;
    }
}

/**
 * تنظيف المدخلات لمنع هجمات SQL Injection
 * @param string $input المدخل المراد تنظيفه
 * @return string المدخل بعد التنظيف
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}
?>