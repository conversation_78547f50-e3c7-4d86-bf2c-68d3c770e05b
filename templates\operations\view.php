<?php
/**
 * قالب عرض تفاصيل العملية
 */
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-info-circle me-2"></i>تفاصيل العملية</h2>
        <a href="index.php?page=operations" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة إلى قائمة العمليات
        </a>
    </div>
    
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>بيانات العملية #<?php echo $operation['id']; ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-truck me-2"></i>بيانات السائق</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <th>اسم السائق:</th>
                                    <td><?php echo $operation['driver_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>رقم السيارة:</th>
                                    <td><?php echo $operation['vehicle_number']; ?></td>
                                </tr>
                                <tr>
                                    <th>الموقع:</th>
                                    <td><?php echo $operation['camp_name']; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>بيانات العملية</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <th>نوع العملية:</th>
                                    <td>
                                        <?php if ($operation['operation_type'] == 'entry'): ?>
                                        <span class="badge bg-success">دخول</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">خروج</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>المادة:</th>
                                    <td><?php echo $operation['material_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>الكمية:</th>
                                    <td><?php echo $operation['quantity']; ?></td>
                                </tr>
                                <tr>
                                    <th>التكلفة:</th>
                                    <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th>تاريخ العملية:</th>
                            <td><?php echo date('Y-m-d H:i:s', strtotime($operation['created_at'])); ?></td>
                        </tr>
                        <tr>
                            <th>تم التسجيل بواسطة:</th>
                            <td><?php echo $operation['user_name']; ?></td>
                        </tr>
                        <?php if (!empty($operation['notes'])): ?>
                        <tr>
                            <th>ملاحظات:</th>
                            <td><?php echo $operation['notes']; ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="index.php?page=operations" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى قائمة العمليات
                </a>
                <?php if (hasPermission('view_reports')): ?>
                <a href="index.php?page=reports&type=operation_receipt&id=<?php echo $operation['id']; ?>" class="btn btn-primary" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة إيصال
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>