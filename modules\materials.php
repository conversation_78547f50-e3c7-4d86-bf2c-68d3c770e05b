<?php
/**
 * صفحة إدارة المواد
 */

// التحقق من الصلاحيات
requirePermission('manage_materials');

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'list';

// معالجة نموذج إضافة/تعديل المادة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_material']) || isset($_POST['edit_material'])) {
        // جمع بيانات النموذج
        $materialData = [
            'name' => sanitizeInput($_POST['name']),
            'price' => (float)$_POST['price'],
            'unit' => sanitizeInput($_POST['unit'])
        ];
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($materialData['name'])) {
            $errors[] = 'يرجى إدخال اسم المادة';
        }
        
        if (empty($materialData['price']) || $materialData['price'] <= 0) {
            $errors[] = 'يرجى إدخال سعر صحيح للمادة';
        }
        
        // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
        if (empty($errors)) {
            if (isset($_POST['add_material'])) {
                // إضافة مادة جديدة
                $materialId = insertData('materials', $materialData);
                
                if ($materialId) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'add_material',
                        'details' => json_encode([
                            'material_id' => $materialId,
                            'material_name' => $materialData['name'],
                            'price' => $materialData['price']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المواد مع رسالة نجاح
                    header('Location: index.php?page=materials&success=تم إضافة المادة بنجاح');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء إضافة المادة';
                }
            } else {
                // تعديل مادة موجودة
                $materialId = (int)$_POST['material_id'];
                $result = updateData('materials', $materialData, 'id = ?', [$materialId]);
                
                if ($result) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'edit_material',
                        'details' => json_encode([
                            'material_id' => $materialId,
                            'material_name' => $materialData['name'],
                            'price' => $materialData['price']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المواد مع رسالة نجاح
                    header('Location: index.php?page=materials&success=تم تعديل المادة بنجاح');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء تعديل المادة';
                }
            }
        }
    }
}

// عرض الصفحة المطلوبة
switch ($action) {
    case 'add':
        // عرض نموذج إضافة مادة جديدة
        include 'templates/materials/add.php';
        break;
        
    case 'edit':
        // عرض نموذج تعديل مادة
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $materialId = (int)$_GET['id'];
            
            // الحصول على بيانات المادة
            $materialQuery = "SELECT * FROM materials WHERE id = ?";
            $material = fetchRow($materialQuery, [$materialId]);
            
            if ($material) {
                include 'templates/materials/edit.php';
            } else {
                echo '<div class="alert alert-danger">المادة غير موجودة</div>';
                include 'templates/materials/list.php';
            }
        } else {
            echo '<div class="alert alert-danger">معرف المادة غير صحيح</div>';
            include 'templates/materials/list.php';
        }
        break;
        
    default:
        // عرض قائمة المواد
        $materialsQuery = "SELECT m.*, 
                          (SELECT COUNT(*) FROM operations WHERE material_id = m.id) as operations_count 
                          FROM materials m 
                          ORDER BY m.name";
        $materials = fetchAll($materialsQuery);
        
        include 'templates/materials/list.php';
        break;
}
?>