<?php
/**
 * ملف إعدادات قاعدة البيانات
 */

// معلومات الاتصال بقاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'car_management');

// إنشاء اتصال بقاعدة البيانات
function connectDB() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // التحقق من الاتصال
    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // تعيين ترميز الاتصال إلى UTF-8 لدعم اللغة العربية
    $conn->set_charset("utf8");
    
    return $conn;
}

// دالة للتعامل مع استعلامات قاعدة البيانات بشكل آمن
function executeQuery($sql, $params = []) {
    $conn = connectDB();
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $types = '';
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
        }
        
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $stmt->close();
    $conn->close();
    
    return $result;
}

// دالة للحصول على صف واحد من نتيجة الاستعلام
function fetchRow($sql, $params = []) {
    $result = executeQuery($sql, $params);
    return $result->fetch_assoc();
}

// دالة للحصول على جميع الصفوف من نتيجة الاستعلام
function fetchAll($sql, $params = []) {
    $result = executeQuery($sql, $params);
    return $result->fetch_all(MYSQLI_ASSOC);
}

// دالة لإدراج بيانات في قاعدة البيانات
function insertData($table, $data) {
    $conn = connectDB();
    
    $columns = implode(", ", array_keys($data));
    $placeholders = implode(", ", array_fill(0, count($data), "?"));
    
    $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
    $stmt = $conn->prepare($sql);
    
    $types = '';
    $values = [];
    
    foreach ($data as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
        
        $values[] = $value;
    }
    
    $stmt->bind_param($types, ...$values);
    $stmt->execute();
    
    $insertId = $conn->insert_id;
    
    $stmt->close();
    $conn->close();
    
    return $insertId;
}

// دالة لتحديث بيانات في قاعدة البيانات
function updateData($table, $data, $where, $whereParams = []) {
    $conn = connectDB();
    
    $setClauses = [];
    foreach (array_keys($data) as $column) {
        $setClauses[] = "$column = ?";
    }
    
    $setClause = implode(", ", $setClauses);
    
    $sql = "UPDATE $table SET $setClause WHERE $where";
    $stmt = $conn->prepare($sql);
    
    $types = '';
    $values = [];
    
    foreach ($data as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
        
        $values[] = $value;
    }
    
    foreach ($whereParams as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
        
        $values[] = $value;
    }
    
    $stmt->bind_param($types, ...$values);
    $stmt->execute();
    
    $affectedRows = $stmt->affected_rows;
    
    $stmt->close();
    $conn->close();
    
    return $affectedRows;
}

// دالة لحذف بيانات من قاعدة البيانات
function deleteData($table, $where, $params = []) {
    $conn = connectDB();
    
    $sql = "DELETE FROM $table WHERE $where";
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $types = '';
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
        }
        
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    $affectedRows = $stmt->affected_rows;
    
    $stmt->close();
    $conn->close();
    
    return $affectedRows;
}
?>