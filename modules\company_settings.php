<?php
/**
 * صفحة إعدادات الشركة
 */

// التحقق من الصلاحيات
requirePermission('manage_companies');

// معالجة نموذج تعديل الشركة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_company'])) {
    // جمع بيانات النموذج
    $companyData = [
        'name' => sanitizeInput($_POST['name']),
        'address' => sanitizeInput($_POST['address']),
        'phone' => sanitizeInput($_POST['phone']),
        'email' => sanitizeInput($_POST['email'])
    ];
    
    // التحقق من البيانات
    $errors = [];
    
    if (empty($companyData['name'])) {
        $errors[] = 'يرجى إدخال اسم الشركة';
    }
    
    // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
    if (empty($errors)) {
        $companyId = (int)$_POST['company_id'];
        $result = updateData('companies', $companyData, 'id = ?', [$companyId]);
        
        if ($result) {
            // تسجيل النشاط
            $logData = [
                'user_id' => $_SESSION['user_id'],
                'action' => 'edit_company',
                'details' => json_encode([
                    'company_id' => $companyId,
                    'company_name' => $companyData['name']
                ]),
                'ip_address' => $_SERVER['REMOTE_ADDR']
            ];
            insertData('activity_logs', $logData);
            
            // إعادة توجيه مع رسالة نجاح
            header('Location: index.php?page=company_settings&success=تم تعديل بيانات الشركة بنجاح');
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء تعديل بيانات الشركة';
        }
    }
}

// الحصول على بيانات الشركة
$companyQuery = "SELECT * FROM companies LIMIT 1";
$company = fetchRow($companyQuery);

if (!$company) {
    // إذا لم تكن الشركة موجودة، قم بإنشائها
    $companyData = [
        'name' => 'النرجس',
        'address' => '',
        'phone' => '',
        'email' => ''
    ];
    
    $companyId = insertData('companies', $companyData);
    
    if ($companyId) {
        // تسجيل النشاط
        $logData = [
            'user_id' => $_SESSION['user_id'],
            'action' => 'add_company',
            'details' => json_encode([
                'company_id' => $companyId,
                'company_name' => $companyData['name']
            ]),
            'ip_address' => $_SERVER['REMOTE_ADDR']
        ];
        insertData('activity_logs', $logData);
        
        // إعادة الحصول على بيانات الشركة
        $company = fetchRow("SELECT * FROM companies WHERE id = ?", [$companyId]);
    }
}

// عرض نموذج تعديل الشركة
include 'templates/company_settings/edit.php';
?>