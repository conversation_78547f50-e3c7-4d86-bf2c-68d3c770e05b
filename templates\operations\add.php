<?php
/**
 * قالب إضافة عملية جديدة
 */

// الحصول على قائمة السائقين
$driversQuery = "SELECT id, name, vehicle_number FROM drivers";
if (!hasPermission('manage_companies')) {
    $driversQuery .= " WHERE camp_id = ?";
    $drivers = fetchAll($driversQuery, [$_SESSION['camp_id']]);
} else {
    $drivers = fetchAll($driversQuery);
}

// الحصول على قائمة المواد
$materialsQuery = "SELECT id, name, price FROM materials ORDER BY name";
$materials = fetchAll($materialsQuery);

// الحصول على قائمة المواقع إذا كان المستخدم مدير النظام
if (hasPermission('manage_companies')) {
    $campsQuery = "SELECT id, name FROM camps ORDER BY name";
    $camps = fetchAll($campsQuery);
}

// التحقق من وجود معرف السائق (في حالة المسح من QR Code)
$selectedDriverId = isset($_GET['driver_id']) ? (int)$_GET['driver_id'] : null;
$selectedDriver = null;

if ($selectedDriverId) {
    $driverQuery = "SELECT * FROM drivers WHERE id = ?";
    $selectedDriver = fetchRow($driverQuery, [$selectedDriverId]);
}
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-plus-circle me-2"></i>إضافة عملية جديدة</h2>
        <a href="index.php?page=operations" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة إلى قائمة العمليات
        </a>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>بيانات العملية</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=operations&action=add">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="driver_id" class="form-label">السائق <span class="text-danger">*</span></label>
                        <select name="driver_id" id="driver_id" class="form-select" required>
                            <option value="">اختر السائق</option>
                            <?php foreach ($drivers as $driver): ?>
                            <option value="<?php echo $driver['id']; ?>" <?php echo ($selectedDriverId && $selectedDriverId == $driver['id']) ? 'selected' : ''; ?>>
                                <?php echo $driver['name']; ?> (<?php echo $driver['vehicle_number']; ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="material_id" class="form-label">المادة <span class="text-danger">*</span></label>
                        <select name="material_id" id="material_id" class="form-select" required>
                            <option value="">اختر المادة</option>
                            <?php foreach ($materials as $material): ?>
                            <option value="<?php echo $material['id']; ?>" data-price="<?php echo $material['price']; ?>">
                                <?php echo $material['name']; ?> (<?php echo number_format($material['price']); ?> د.ع)
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="operation_type" class="form-label">نوع العملية <span class="text-danger">*</span></label>
                        <select name="operation_type" id="operation_type" class="form-select" required>
                            <option value="">اختر نوع العملية</option>
                            <option value="entry">دخول</option>
                            <option value="exit">خروج</option>
                        </select>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="quantity" class="form-label">الكمية</label>
                        <input type="number" name="quantity" id="quantity" class="form-control" value="1" min="0.1" step="0.1" required>
                    </div>
                </div>
                
                <?php if (hasPermission('manage_companies')): ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="camp_id" class="form-label">الموقع <span class="text-danger">*</span></label>
                        <select name="camp_id" id="camp_id" class="form-select" required>
                            <option value="">اختر الموقع</option>
                            <?php foreach ($camps as $camp): ?>
                            <option value="<?php echo $camp['id']; ?>">
                                <?php echo $camp['name']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="cost" class="form-label">التكلفة المقدرة</label>
                        <div class="input-group">
                            <input type="text" id="cost" class="form-control" readonly>
                            <span class="input-group-text">د.ع</span>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="cost" class="form-label">التكلفة المقدرة</label>
                        <div class="input-group">
                            <input type="text" id="cost" class="form-control" readonly>
                            <span class="input-group-text">د.ع</span>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="text-center">
                    <button type="submit" name="add_operation" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ العملية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// حساب التكلفة تلقائيًا عند تغيير المادة أو الكمية
document.addEventListener('DOMContentLoaded', function() {
    const materialSelect = document.getElementById('material_id');
    const quantityInput = document.getElementById('quantity');
    const costInput = document.getElementById('cost');
    
    function calculateCost() {
        const selectedOption = materialSelect.options[materialSelect.selectedIndex];
        if (selectedOption.value) {
            const price = parseFloat(selectedOption.dataset.price);
            const quantity = parseFloat(quantityInput.value);
            const cost = price * quantity;
            costInput.value = cost.toLocaleString('ar-IQ');
        } else {
            costInput.value = '';
        }
    }
    
    materialSelect.addEventListener('change', calculateCost);
    quantityInput.addEventListener('input', calculateCost);
    
    // حساب التكلفة عند تحميل الصفحة
    calculateCost();
});
</script>