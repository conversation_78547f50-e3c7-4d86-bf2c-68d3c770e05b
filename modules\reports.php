<?php
/**
 * صفحة التقارير
 */

// التحقق من الصلاحيات
requirePermission('view_reports');

// تحديد نوع التقرير المطلوب
$reportType = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'operations';

// معالجة طلب تصدير التقرير
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    // تصدير التقرير بتنسيق Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="تقرير_' . $reportType . '_' . date('Y-m-d') . '.xls"');
    header('Cache-Control: max-age=0');
}

// عرض التقرير المطلوب
switch ($reportType) {
    case 'operations':
        // تقرير العمليات
        // تحديد معايير التصفية
        $filters = [];
        $params = [];
        
        // تصفية حسب الموقع
        if (isset($_GET['camp_id']) && !empty($_GET['camp_id']) && hasPermission('manage_companies')) {
            $filters[] = "o.camp_id = ?";
            $params[] = (int)$_GET['camp_id'];
        } elseif (!hasPermission('manage_companies')) {
            $filters[] = "o.camp_id = ?";
            $params[] = $_SESSION['camp_id'];
        }
        
        // تصفية حسب السائق
        if (isset($_GET['driver_id']) && !empty($_GET['driver_id'])) {
            $filters[] = "o.driver_id = ?";
            $params[] = (int)$_GET['driver_id'];
        }
        
        // تصفية حسب المادة
        if (isset($_GET['material_id']) && !empty($_GET['material_id'])) {
            $filters[] = "o.material_id = ?";
            $params[] = (int)$_GET['material_id'];
        }
        
        // تصفية حسب نوع العملية
        if (isset($_GET['operation_type']) && !empty($_GET['operation_type'])) {
            $filters[] = "o.operation_type = ?";
            $params[] = sanitizeInput($_GET['operation_type']);
        }
        
        // تصفية حسب التاريخ
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $filters[] = "DATE(o.created_at) >= ?";
            $params[] = sanitizeInput($_GET['date_from']);
        }
        
        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $filters[] = "DATE(o.created_at) <= ?";
            $params[] = sanitizeInput($_GET['date_to']);
        }
        
        // بناء استعلام قائمة العمليات
        $operationsQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name, c.name as camp_name, u.name as user_name 
                           FROM operations o 
                           LEFT JOIN drivers d ON o.driver_id = d.id 
                           LEFT JOIN materials m ON o.material_id = m.id 
                           LEFT JOIN camps c ON o.camp_id = c.id 
                           LEFT JOIN users u ON o.created_by = u.id";
        
        if (!empty($filters)) {
            $operationsQuery .= " WHERE " . implode(" AND ", $filters);
        }
        
        // ترتيب النتائج
        $operationsQuery .= " ORDER BY o.created_at DESC";
        
        // تنفيذ الاستعلام
        $operations = fetchAll($operationsQuery, $params);
        
        // حساب الإجماليات
        $totalCost = 0;
        $entryCount = 0;
        $exitCount = 0;
        
        foreach ($operations as $operation) {
            $totalCost += $operation['cost'];
            if ($operation['operation_type'] == 'entry') {
                $entryCount++;
            } else {
                $exitCount++;
            }
        }
        
        // الحصول على قائمة الكمبات للتصفية
        if (hasPermission('manage_companies')) {
            $campsQuery = "SELECT id, name FROM camps ORDER BY name";
            $camps = fetchAll($campsQuery);
        }
        
        // الحصول على قائمة السائقين للتصفية
        $driversQuery = "SELECT id, name, vehicle_number FROM drivers";
        if (!hasPermission('manage_companies')) {
            $driversQuery .= " WHERE camp_id = ?";
            $drivers = fetchAll($driversQuery, [$_SESSION['camp_id']]);
        } else {
            $drivers = fetchAll($driversQuery);
        }
        
        // الحصول على قائمة المواد للتصفية
        $materialsQuery = "SELECT id, name FROM materials ORDER BY name";
        $materials = fetchAll($materialsQuery);
        
        include 'templates/reports/operations.php';
        break;
        
    case 'drivers':
        // تقرير السائقين
        // تحديد معايير التصفية
        $filters = [];
        $params = [];
        
        // تصفية حسب الموقع
        if (isset($_GET['camp_id']) && !empty($_GET['camp_id']) && hasPermission('manage_companies')) {
            $filters[] = "d.camp_id = ?";
            $params[] = (int)$_GET['camp_id'];
        } elseif (!hasPermission('manage_companies')) {
            $filters[] = "d.camp_id = ?";
            $params[] = $_SESSION['camp_id'];
        }
        
        // بناء استعلام قائمة السائقين
        $driversQuery = "SELECT d.*, c.name as camp_name, 
                        (SELECT COUNT(*) FROM operations WHERE driver_id = d.id) as operations_count,
                        (SELECT SUM(cost) FROM operations WHERE driver_id = d.id) as total_cost
                        FROM drivers d 
                        LEFT JOIN camps c ON d.camp_id = c.id";
        
        if (!empty($filters)) {
            $driversQuery .= " WHERE " . implode(" AND ", $filters);
        }
        
        // ترتيب النتائج
        $driversQuery .= " ORDER BY d.name";
        
        // تنفيذ الاستعلام
        $drivers = fetchAll($driversQuery, $params);
        
        // الحصول على قائمة المواقع للتصفية
        if (hasPermission('manage_companies')) {
            $campsQuery = "SELECT id, name FROM camps ORDER BY name";
            $camps = fetchAll($campsQuery);
        }
        
        include 'templates/reports/drivers.php';
        break;
        
    case 'daily':
        // تقرير يومي
        // تحديد التاريخ المطلوب
        $date = isset($_GET['date']) ? sanitizeInput($_GET['date']) : date('Y-m-d');
        
        // تحديد الموقع
        $campId = null;
        if (isset($_GET['camp_id']) && !empty($_GET['camp_id']) && hasPermission('manage_companies')) {
            $campId = (int)$_GET['camp_id'];
        } elseif (!hasPermission('manage_companies')) {
            $campId = $_SESSION['camp_id'];
        }
        
        // بناء استعلام العمليات اليومية
        $operationsQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name, c.name as camp_name 
                           FROM operations o 
                           LEFT JOIN drivers d ON o.driver_id = d.id 
                           LEFT JOIN materials m ON o.material_id = m.id 
                           LEFT JOIN camps c ON o.camp_id = c.id 
                           WHERE DATE(o.created_at) = ?";
        
        $params = [$date];
        
        if ($campId) {
            $operationsQuery .= " AND o.camp_id = ?";
            $params[] = $campId;
        }
        
        // ترتيب النتائج
        $operationsQuery .= " ORDER BY o.created_at";
        
        // تنفيذ الاستعلام
        $operations = fetchAll($operationsQuery, $params);
        
        // حساب الإجماليات
        $totalCost = 0;
        $entryCount = 0;
        $exitCount = 0;
        
        foreach ($operations as $operation) {
            $totalCost += $operation['cost'];
            if ($operation['operation_type'] == 'entry') {
                $entryCount++;
            } else {
                $exitCount++;
            }
        }
        
        // الحصول على قائمة المواقع للتصفية
        if (hasPermission('manage_companies')) {
            $campsQuery = "SELECT id, name FROM camps ORDER BY name";
            $camps = fetchAll($campsQuery);
        }
        
        include 'templates/reports/daily.php';
        break;
        
    case 'operation_receipt':
        // إيصال عملية
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $operationId = (int)$_GET['id'];
            
            // الحصول على بيانات العملية
            $operationQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name, c.name as camp_name, u.name as user_name 
                               FROM operations o 
                               LEFT JOIN drivers d ON o.driver_id = d.id 
                               LEFT JOIN materials m ON o.material_id = m.id 
                               LEFT JOIN camps c ON o.camp_id = c.id 
                               LEFT JOIN users u ON o.created_by = u.id 
                               WHERE o.id = ?";
            
            // إضافة شرط الموقع إذا لم يكن المستخدم مدير النظام
            if (!hasPermission('manage_companies')) {
                $operationQuery .= " AND o.camp_id = ?";
                $operation = fetchRow($operationQuery, [$operationId, $_SESSION['camp_id']]);
            } else {
                $operation = fetchRow($operationQuery, [$operationId]);
            }
            
            if ($operation) {
                include 'templates/reports/operation_receipt.php';
            } else {
                echo '<div class="alert alert-danger">العملية غير موجودة أو ليس لديك صلاحية للوصول إليها</div>';
            }
        } else {
            echo '<div class="alert alert-danger">معرف العملية غير صحيح</div>';
        }
        break;
        
    default:
        // التقرير الافتراضي
        include 'templates/reports/operations.php';
        break;
}
?>