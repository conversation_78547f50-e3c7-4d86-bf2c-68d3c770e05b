<?php
/**
 * صفحة إدارة المستخدمين
 */

// التحقق من الصلاحيات
requirePermission('manage_users');

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'list';

// معالجة نموذج إضافة/تعديل المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_user']) || isset($_POST['edit_user'])) {
        // جمع بيانات النموذج
        $userData = [
            'name' => sanitizeInput($_POST['name']),
            'username' => sanitizeInput($_POST['username']),
            'email' => sanitizeInput($_POST['email']),
            'phone' => sanitizeInput($_POST['phone']),
            'role' => sanitizeInput($_POST['role']),
            'status' => isset($_POST['status']) ? 1 : 0
        ];
        
        // تعيين الشركة والموقع حسب الدور
        if ($userData['role'] == 'admin') {
            $userData['company_id'] = isset($_POST['company_id']) ? (int)$_POST['company_id'] : null;
            $userData['camp_id'] = null;
        } else {
            $userData['company_id'] = isset($_POST['company_id']) ? (int)$_POST['company_id'] : null;
            $userData['camp_id'] = isset($_POST['camp_id']) ? (int)$_POST['camp_id'] : null;
        }
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($userData['name'])) {
            $errors[] = 'يرجى إدخال اسم المستخدم';
        }
        
        if (empty($userData['username'])) {
            $errors[] = 'يرجى إدخال اسم المستخدم للدخول';
        }
        
        if (empty($userData['role'])) {
            $errors[] = 'يرجى اختيار دور المستخدم';
        }
        
        if ($userData['role'] != 'admin' && empty($userData['camp_id'])) {
            $errors[] = 'يرجى اختيار الموقع للمستخدم';
        }
        
        // التحقق من عدم تكرار اسم المستخدم
        $usernameQuery = "SELECT id FROM users WHERE username = ?";
        $usernameParams = [$userData['username']];
        
        if (isset($_POST['edit_user'])) {
            $userId = (int)$_POST['user_id'];
            $usernameQuery .= " AND id != ?";
            $usernameParams[] = $userId;
        }
        
        $existingUser = fetchRow($usernameQuery, $usernameParams);
        
        if ($existingUser) {
            $errors[] = 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر';
        }
        
        // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
        if (empty($errors)) {
            if (isset($_POST['add_user'])) {
                // إضافة كلمة مرور للمستخدم الجديد
                if (empty($_POST['password'])) {
                    $errors[] = 'يرجى إدخال كلمة المرور';
                } else {
                    $userData['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                if (empty($errors)) {
                    // إضافة مستخدم جديد
                    $userId = insertData('users', $userData);
                    
                    if ($userId) {
                        // تسجيل النشاط
                        $logData = [
                            'user_id' => $_SESSION['user_id'],
                            'action' => 'add_user',
                            'details' => json_encode([
                                'user_id' => $userId,
                                'username' => $userData['username'],
                                'role' => $userData['role']
                            ]),
                            'ip_address' => $_SERVER['REMOTE_ADDR']
                        ];
                        insertData('activity_logs', $logData);
                        
                        // إعادة توجيه إلى قائمة المستخدمين مع رسالة نجاح
                        header('Location: index.php?page=users&success=تم إضافة المستخدم بنجاح');
                        exit;
                    } else {
                        $errors[] = 'حدث خطأ أثناء إضافة المستخدم';
                    }
                }
            } else {
                // تعديل مستخدم موجود
                $userId = (int)$_POST['user_id'];
                
                // تحديث كلمة المرور إذا تم إدخالها
                if (!empty($_POST['password'])) {
                    $userData['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                $result = updateData('users', $userData, 'id = ?', [$userId]);
                
                if ($result) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'edit_user',
                        'details' => json_encode([
                            'user_id' => $userId,
                            'username' => $userData['username'],
                            'role' => $userData['role']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المستخدمين مع رسالة نجاح
                    header('Location: index.php?page=users&success=تم تعديل المستخدم بنجاح');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء تعديل المستخدم';
                }
            }
        }
    }
}

// عرض الصفحة المطلوبة
switch ($action) {
    case 'add':
        // الحصول على قائمة الشركات والمواقع
        $companiesQuery = "SELECT id, name FROM companies ORDER BY name";
        $companies = fetchAll($companiesQuery);
        
        $campsQuery = "SELECT id, name, company_id FROM camps ORDER BY name";
        $camps = fetchAll($campsQuery);
        
        // عرض نموذج إضافة مستخدم جديد
        include 'templates/users/add.php';
        break;
        
    case 'edit':
        // عرض نموذج تعديل مستخدم
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $userId = (int)$_GET['id'];
            
            // الحصول على بيانات المستخدم
            $userQuery = "SELECT * FROM users WHERE id = ?";
            $user = fetchRow($userQuery, [$userId]);
            
            if ($user) {
                // الحصول على قائمة الشركات والمواقع
                $companiesQuery = "SELECT id, name FROM companies ORDER BY name";
                $companies = fetchAll($companiesQuery);
                
                $campsQuery = "SELECT id, name, company_id FROM camps ORDER BY name";
                $camps = fetchAll($campsQuery);
                
                include 'templates/users/edit.php';
            } else {
                echo '<div class="alert alert-danger">المستخدم غير موجود</div>';
                include 'templates/users/list.php';
            }
        } else {
            echo '<div class="alert alert-danger">معرف المستخدم غير صحيح</div>';
            include 'templates/users/list.php';
        }
        break;
        
    case 'view':
        // عرض تفاصيل المستخدم
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $userId = (int)$_GET['id'];
            
            // الحصول على بيانات المستخدم
            $userQuery = "SELECT u.*, c.name as camp_name, co.name as company_name 
                         FROM users u 
                         LEFT JOIN camps c ON u.camp_id = c.id 
                         LEFT JOIN companies co ON u.company_id = co.id 
                         WHERE u.id = ?";
            $user = fetchRow($userQuery, [$userId]);
            
            if ($user) {
                // الحصول على سجل نشاط المستخدم
                $logsQuery = "SELECT * FROM activity_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT 50";
                $logs = fetchAll($logsQuery, [$userId]);
                
                include 'templates/users/view.php';
            } else {
                echo '<div class="alert alert-danger">المستخدم غير موجود</div>';
                include 'templates/users/list.php';
            }
        } else {
            echo '<div class="alert alert-danger">معرف المستخدم غير صحيح</div>';
            include 'templates/users/list.php';
        }
        break;
        
    default:
        // عرض قائمة المستخدمين
        $usersQuery = "SELECT u.*, c.name as camp_name, co.name as company_name 
                      FROM users u 
                      LEFT JOIN camps c ON u.camp_id = c.id 
                      LEFT JOIN companies co ON u.company_id = co.id 
                      ORDER BY u.role, u.name";
        $users = fetchAll($usersQuery);
        
        include 'templates/users/list.php';
        break;
}

/**
 * دالة لتحويل دور المستخدم إلى نص مقروء
 * @param string $role دور المستخدم
 * @return string النص المقروء
 */
function getUserRoleText($role) {
    switch ($role) {
        case 'admin':
            return 'مدير النظام';
        case 'camp_manager':
            return 'مدير موقع';
        case 'accountant':
            return 'محاسب';
        case 'worker':
            return 'عامل';
        default:
            return 'غير معروف';
    }
}
?>