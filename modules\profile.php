<?php

/**
 * صفحة الملف الشخصي المحسنة
 */

// التحقق من تسجيل الدخول
requireLogin();

// معالجة تحديث الملف الشخصي
$success = '';
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        // جمع بيانات النموذج
        $profileData = [
            'name' => sanitizeInput($_POST['name']),
            'email' => sanitizeInput($_POST['email']),
            'phone' => sanitizeInput($_POST['phone'])
        ];

        // التحقق من صحة البيانات
        if (empty($profileData['name'])) {
            $errors[] = 'الاسم مطلوب';
        }

        if (!empty($profileData['email']) && !filter_var($profileData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        // تحديث البيانات إذا لم تكن هناك أخطاء
        if (empty($errors)) {
            $result = updateData('users', $profileData, 'id = ?', [$_SESSION['user_id']]);

            if ($result) {
                // تسجيل النشاط
                $logData = [
                    'user_id' => $_SESSION['user_id'],
                    'action' => 'update_profile',
                    'details' => 'تم تحديث الملف الشخصي',
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ];
                insertData('activity_logs', $logData);

                // تحديث اسم المستخدم في الجلسة
                $_SESSION['user_name'] = $profileData['name'];

                $success = 'تم تحديث الملف الشخصي بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث الملف الشخصي';
            }
        }
    } elseif (isset($_POST['change_password'])) {
        // تغيير كلمة المرور
        $currentPassword = $_POST['current_password'];
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];

        // التحقق من كلمة المرور الحالية
        $userQuery = "SELECT password FROM users WHERE id = ?";
        $user = fetchRow($userQuery, [$_SESSION['user_id']]);

        if (!password_verify($currentPassword, $user['password'])) {
            $errors[] = 'كلمة المرور الحالية غير صحيحة';
        } elseif (strlen($newPassword) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } elseif ($newPassword !== $confirmPassword) {
            $errors[] = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        } else {
            // تحديث كلمة المرور
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $result = updateData('users', ['password' => $hashedPassword], 'id = ?', [$_SESSION['user_id']]);

            if ($result) {
                // تسجيل النشاط
                $logData = [
                    'user_id' => $_SESSION['user_id'],
                    'action' => 'change_password',
                    'details' => 'تم تغيير كلمة المرور',
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ];
                insertData('activity_logs', $logData);

                $success = 'تم تغيير كلمة المرور بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء تغيير كلمة المرور';
            }
        }
    }
}

// الحصول على بيانات المستخدم الحالية
$userQuery = "SELECT u.*, c.name as camp_name, co.name as company_name
              FROM users u
              LEFT JOIN camps c ON u.camp_id = c.id
              LEFT JOIN companies co ON u.company_id = co.id
              WHERE u.id = ?";
$userInfo = fetchRow($userQuery, [$_SESSION['user_id']]);

// الحصول على إحصائيات المستخدم
$statsQuery = "SELECT
                COUNT(*) as total_operations,
                COUNT(CASE WHEN operation_type = 'entry' THEN 1 END) as entry_operations,
                COUNT(CASE WHEN operation_type = 'exit' THEN 1 END) as exit_operations,
                SUM(cost) as total_cost
                FROM operations
                WHERE created_by = ?";
$userStats = fetchRow($statsQuery, [$_SESSION['user_id']]);

// الحصول على آخر الأنشطة
$logsQuery = "SELECT * FROM activity_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT 10";
$recentLogs = fetchAll($logsQuery, [$_SESSION['user_id']]);

// الحصول على معلومات تسجيل الدخول
$loginQuery = "SELECT last_login, created_at FROM users WHERE id = ?";
$loginInfo = fetchRow($loginQuery, [$_SESSION['user_id']]);

// تحديد أسماء الأدوار
$roleNames = [
    'admin' => 'مدير النظام',
    'camp_manager' => 'مدير الموقع',
    'accountant' => 'محاسب',
    'worker' => 'موظف'
];

// تحديد أسماء الأنشطة
$actionNames = [
    'login' => 'تسجيل دخول',
    'logout' => 'تسجيل خروج',
    'add_operation' => 'إضافة عملية',
    'edit_operation' => 'تعديل عملية',
    'delete_operation' => 'حذف عملية',
    'add_driver' => 'إضافة سائق',
    'edit_driver' => 'تعديل سائق',
    'delete_driver' => 'حذف سائق',
    'update_profile' => 'تحديث الملف الشخصي',
    'change_password' => 'تغيير كلمة المرور'
];
?>

<div class="profile-page">
    <!-- رسائل النجاح والأخطاء -->
    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="material-icons me-2">check_circle</i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="material-icons me-2">error</i>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- معلومات المستخدم الأساسية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card profile-header">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="profile-avatar">
                                <i class="material-icons">account_circle</i>
                            </div>
                        </div>
                        <div class="col">
                            <h4 class="mb-1"><?php echo htmlspecialchars($userInfo['name']); ?></h4>
                            <p class="text-muted mb-1">
                                <i class="material-icons me-1">badge</i>
                                <?php echo $roleNames[$userInfo['role']] ?? $userInfo['role']; ?>
                            </p>
                            <?php if (!empty($userInfo['company_name'])): ?>
                                <p class="text-muted mb-1">
                                    <i class="material-icons me-1">business</i>
                                    <?php echo htmlspecialchars($userInfo['company_name']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if (!empty($userInfo['camp_name'])): ?>
                                <p class="text-muted mb-0">
                                    <i class="material-icons me-1">location_on</i>
                                    <?php echo htmlspecialchars($userInfo['camp_name']); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-<?php echo $userInfo['status'] ? 'success' : 'danger'; ?> fs-6">
                                <i class="material-icons me-1" style="font-size: 16px;">
                                    <?php echo $userInfo['status'] ? 'check_circle' : 'cancel'; ?>
                                </i>
                                <?php echo $userInfo['status'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المستخدم -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="material-icons stats-icon">assignment</i>
                    <h3 class="stats-number"><?php echo number_format($userStats['total_operations'] ?? 0); ?></h3>
                    <p class="stats-label">إجمالي العمليات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="material-icons stats-icon">input</i>
                    <h3 class="stats-number"><?php echo number_format($userStats['entry_operations'] ?? 0); ?></h3>
                    <p class="stats-label">عمليات الدخول</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="material-icons stats-icon">output</i>
                    <h3 class="stats-number"><?php echo number_format($userStats['exit_operations'] ?? 0); ?></h3>
                    <p class="stats-label">عمليات الخروج</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="material-icons stats-icon">attach_money</i>
                    <h3 class="stats-number"><?php echo number_format($userStats['total_cost'] ?? 0); ?></h3>
                    <p class="stats-label">إجمالي التكلفة (د.ع)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- تحديث الملف الشخصي -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="material-icons me-2">edit</i>
                        تحديث الملف الشخصي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="name" name="name"
                                    value="<?php echo htmlspecialchars($userInfo['name']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username"
                                    value="<?php echo htmlspecialchars($userInfo['username']); ?>" disabled>
                                <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email"
                                    value="<?php echo htmlspecialchars($userInfo['email'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                    value="<?php echo htmlspecialchars($userInfo['phone'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">الدور</label>
                                <input type="text" class="form-control" id="role"
                                    value="<?php echo $roleNames[$userInfo['role']] ?? $userInfo['role']; ?>" disabled>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="created_at" class="form-label">تاريخ الإنشاء</label>
                                <input type="text" class="form-control" id="created_at"
                                    value="<?php echo date('Y-m-d H:i', strtotime($userInfo['created_at'])); ?>" disabled>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="material-icons me-2">save</i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تغيير كلمة المرور -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="material-icons me-2">lock</i>
                        تغيير كلمة المرور
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password"
                                    minlength="6" required>
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                    minlength="6" required>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="material-icons me-2">security</i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <!-- معلومات تسجيل الدخول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="material-icons me-2">schedule</i>
                        معلومات الجلسة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <i class="material-icons text-primary">login</i>
                        <div>
                            <strong>آخر تسجيل دخول</strong>
                            <p class="text-muted mb-0">
                                <?php
                                if ($loginInfo['last_login']) {
                                    echo date('Y-m-d H:i', strtotime($loginInfo['last_login']));
                                } else {
                                    echo 'لم يتم تسجيل الدخول من قبل';
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="material-icons text-success">person_add</i>
                        <div>
                            <strong>عضو منذ</strong>
                            <p class="text-muted mb-0">
                                <?php echo date('Y-m-d', strtotime($loginInfo['created_at'])); ?>
                            </p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="material-icons text-info">computer</i>
                        <div>
                            <strong>عنوان IP الحالي</strong>
                            <p class="text-muted mb-0"><?php echo $_SERVER['REMOTE_ADDR']; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الأنشطة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="material-icons me-2">history</i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentLogs)): ?>
                        <div class="activity-list">
                            <?php foreach ($recentLogs as $log): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="material-icons">
                                            <?php
                                            switch ($log['action']) {
                                                case 'login':
                                                    echo 'login';
                                                    break;
                                                case 'logout':
                                                    echo 'logout';
                                                    break;
                                                case 'add_operation':
                                                    echo 'add';
                                                    break;
                                                case 'edit_operation':
                                                    echo 'edit';
                                                    break;
                                                case 'delete_operation':
                                                    echo 'delete';
                                                    break;
                                                default:
                                                    echo 'history';
                                                    break;
                                            }
                                            ?>
                                        </i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="mb-1">
                                            <?php echo $actionNames[$log['action']] ?? $log['action']; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($log['timestamp'])); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد أنشطة حديثة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    // التحقق من تطابق كلمة المرور
    document.addEventListener('DOMContentLoaded', function() {
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');

        function checkPasswordMatch() {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }

        newPassword.addEventListener('input', checkPasswordMatch);
        confirmPassword.addEventListener('input', checkPasswordMatch);
    });
</script>