<?php
/**
 * صفحة تسجيل الخروج
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من تسجيل دخول المستخدم
if (isset($_SESSION['user_id'])) {
    // تسجيل عملية تسجيل الخروج
    $logData = [
        'user_id' => $_SESSION['user_id'],
        'action' => 'logout',
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    insertData('activity_logs', $logData);
    
    // إنهاء الجلسة
    session_unset();
    session_destroy();
}

// توجيه المستخدم إلى صفحة تسجيل الدخول
header('Location: login.php');
exit;
?>