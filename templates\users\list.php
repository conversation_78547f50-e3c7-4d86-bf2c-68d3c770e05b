<?php
/**
 * قالب عرض قائمة المستخدمين
 */
?>

<div class="container-fluid py-4">
    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i><?php echo $_GET['success']; ?>
    </div>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php?page=dashboard">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">المستخدمين</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة المستخدمين</h5>
                    <a href="index.php?page=users&action=add" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i> إضافة مستخدم جديد
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الصلاحية</th>
                                    <th>الشركة</th>
                                    <th>الموقع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">لا يوجد مستخدمين</td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($users as $index => $user): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo htmlspecialchars($user['name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo getUserRoleText($user['role']); ?></td>
                                    <td><?php echo htmlspecialchars($user['company_name'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($user['camp_name'] ?? '-'); ?></td>
                                    <td>
                                        <a href="index.php?page=users&action=view&id=<?php echo $user['id']; ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?page=users&action=edit&id=<?php echo $user['id']; ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>