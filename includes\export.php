<?php
/**
 * ملف وظائف التصدير للتقارير
 */

/**
 * تصدير البيانات إلى ملف Excel
 * @param array $data البيانات المراد تصديرها
 * @param array $headers عناوين الأعمدة
 * @param string $filename اسم الملف
 */
function exportToExcel($data, $headers, $filename) {
    // تعيين نوع المحتوى
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');
    
    // إنشاء ملف Excel
    echo '<table border="1">';
    
    // إضافة عناوين الأعمدة
    echo '<tr>';
    foreach ($headers as $header) {
        echo '<th>' . $header . '</th>';
    }
    echo '</tr>';
    
    // إضافة البيانات
    foreach ($data as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . $cell . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

/**
 * تصدير البيانات إلى ملف PDF
 * @param array $data البيانات المراد تصديرها
 * @param array $headers عناوين الأعمدة
 * @param string $filename اسم الملف
 * @param string $title عنوان التقرير
 */
function exportToPDF($data, $headers, $filename, $title) {
    // تضمين مكتبة TCPDF
    require_once 'vendor/tcpdf/tcpdf.php';
    
    // إنشاء مستند PDF جديد
    $pdf = new TCPDF('L', 'mm', 'A4', true, 'UTF-8', false);
    
    // تعيين معلومات المستند
    $pdf->SetCreator('نظام إدارة عمليات دخول وخروج سيارات الحمل');
    $pdf->SetAuthor('نظام إدارة عمليات دخول وخروج سيارات الحمل');
    $pdf->SetTitle($title);
    
    // تعطيل رأس وتذييل الصفحة
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // تعيين الهوامش
    $pdf->SetMargins(10, 10, 10);
    
    // إضافة صفحة جديدة
    $pdf->AddPage();
    
    // تعيين الخط
    $pdf->SetFont('aealarabiya', '', 14);
    
    // إضافة عنوان التقرير
    $pdf->Cell(0, 10, $title, 0, 1, 'C');
    $pdf->Ln(5);
    
    // إنشاء جدول البيانات
    $pdf->SetFont('aealarabiya', '', 10);
    
    // حساب عرض الأعمدة
    $columnWidth = 270 / count($headers);
    
    // إضافة عناوين الأعمدة
    $pdf->SetFillColor(220, 220, 220);
    foreach ($headers as $header) {
        $pdf->Cell($columnWidth, 10, $header, 1, 0, 'C', true);
    }
    $pdf->Ln();
    
    // إضافة البيانات
    $pdf->SetFillColor(255, 255, 255);
    foreach ($data as $row) {
        foreach ($row as $cell) {
            $pdf->Cell($columnWidth, 10, $cell, 1, 0, 'C');
        }
        $pdf->Ln();
    }
    
    // إضافة تاريخ الإنشاء
    $pdf->Ln(10);
    $pdf->SetFont('aealarabiya', '', 8);
    $pdf->Cell(0, 10, 'تم إنشاء هذا التقرير بتاريخ: ' . date('Y-m-d H:i:s'), 0, 1, 'C');
    
    // إخراج ملف PDF
    $pdf->Output($filename . '.pdf', 'D');
    exit;
}

/**
 * تصدير تقرير العمليات
 * @param array $operations بيانات العمليات
 * @param string $format تنسيق التصدير (excel أو pdf)
 */
function exportOperationsReport($operations, $format) {
    // تحضير البيانات للتصدير
    $data = [];
    $headers = ['#', 'السائق', 'رقم السيارة', 'المادة', 'نوع العملية', 'الكمية', 'التكلفة'];
    
    // إضافة عمود الكمب إذا كان المستخدم مدير النظام
    if (hasPermission('manage_companies')) {
        $headers[] = 'الكمب';
    }
    
    $headers[] = 'التاريخ';
    $headers[] = 'المستخدم';
    
    // تحضير البيانات
    foreach ($operations as $index => $operation) {
        $row = [
            $index + 1,
            $operation['driver_name'],
            $operation['vehicle_number'],
            $operation['material_name'],
            $operation['operation_type'] == 'entry' ? 'دخول' : 'خروج',
            $operation['quantity'],
            number_format($operation['cost']) . ' د.ع'
        ];
        
        // إضافة عمود الكمب إذا كان المستخدم مدير النظام
        if (hasPermission('manage_companies')) {
            $row[] = $operation['camp_name'];
        }
        
        $row[] = date('Y-m-d H:i', strtotime($operation['created_at']));
        $row[] = $operation['user_name'];
        
        $data[] = $row;
    }
    
    // تصدير البيانات حسب التنسيق المطلوب
    if ($format == 'excel') {
        exportToExcel($data, $headers, 'تقرير_العمليات_' . date('Y-m-d'));
    } else {
        exportToPDF($data, $headers, 'تقرير_العمليات_' . date('Y-m-d'), 'تقرير العمليات');
    }
}

/**
 * تصدير تقرير السائقين
 * @param array $drivers بيانات السائقين
 * @param string $format تنسيق التصدير (excel أو pdf)
 */
function exportDriversReport($drivers, $format) {
    // تحضير البيانات للتصدير
    $data = [];
    $headers = ['#', 'اسم السائق', 'رقم الهاتف', 'نوع السيارة', 'رقم السيارة'];
    
    // إضافة عمود الكمب إذا كان المستخدم مدير النظام
    if (hasPermission('manage_companies')) {
        $headers[] = 'الكمب';
    }
    
    $headers[] = 'عدد العمليات';
    $headers[] = 'إجمالي التكاليف';
    
    // تحضير البيانات
    foreach ($drivers as $index => $driver) {
        $row = [
            $index + 1,
            $driver['name'],
            $driver['phone'] ?: '-',
            $driver['vehicle_type'] ?: '-',
            $driver['vehicle_number']
        ];
        
        // إضافة عمود الكمب إذا كان المستخدم مدير النظام
        if (hasPermission('manage_companies')) {
            $row[] = $driver['camp_name'];
        }
        
        $row[] = $driver['operations_count'];
        $row[] = number_format($driver['total_cost'] ?: 0) . ' د.ع';
        
        $data[] = $row;
    }
    
    // تصدير البيانات حسب التنسيق المطلوب
    if ($format == 'excel') {
        exportToExcel($data, $headers, 'تقرير_السائقين_' . date('Y-m-d'));
    } else {
        exportToPDF($data, $headers, 'تقرير_السائقين_' . date('Y-m-d'), 'تقرير السائقين');
    }
}

/**
 * تصدير التقرير اليومي
 * @param array $operations بيانات العمليات
 * @param string $date التاريخ
 * @param string $format تنسيق التصدير (excel أو pdf)
 */
function exportDailyReport($operations, $date, $format) {
    // تحضير البيانات للتصدير
    $data = [];
    $headers = ['#', 'الوقت', 'السائق', 'رقم السيارة', 'المادة', 'نوع العملية', 'الكمية', 'التكلفة'];
    
    // إضافة عمود الكمب إذا كان المستخدم مدير النظام ولم يتم تحديد كمب
    if (hasPermission('manage_companies') && !isset($_GET['camp_id'])) {
        $headers[] = 'الكمب';
    }
    
    // تحضير البيانات
    foreach ($operations as $index => $operation) {
        $row = [
            $index + 1,
            date('H:i', strtotime($operation['created_at'])),
            $operation['driver_name'],
            $operation['vehicle_number'],
            $operation['material_name'],
            $operation['operation_type'] == 'entry' ? 'دخول' : 'خروج',
            $operation['quantity'],
            number_format($operation['cost']) . ' د.ع'
        ];
        
        // إضافة عمود الكمب إذا كان المستخدم مدير النظام ولم يتم تحديد كمب
        if (hasPermission('manage_companies') && !isset($_GET['camp_id'])) {
            $row[] = $operation['camp_name'];
        }
        
        $data[] = $row;
    }
    
    // تصدير البيانات حسب التنسيق المطلوب
    $title = 'التقرير اليومي - ' . $date;
    if ($format == 'excel') {
        exportToExcel($data, $headers, 'التقرير_اليومي_' . $date);
    } else {
        exportToPDF($data, $headers, 'التقرير_اليومي_' . $date, $title);
    }
}
?>