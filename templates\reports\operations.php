<?php
/**
 * قالب تقرير العمليات
 */
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-file-alt me-2"></i>تقرير العمليات</h2>
        <div>
            <a href="index.php?page=reports&type=operations<?php echo isset($_SERVER['QUERY_STRING']) ? '&' . str_replace('page=reports&type=operations&', '', $_SERVER['QUERY_STRING']) : ''; ?>&export=excel" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i>تصدير Excel
            </a>
            <a href="index.php?page=reports&type=operations<?php echo isset($_SERVER['QUERY_STRING']) ? '&' . str_replace('page=reports&type=operations&', '', $_SERVER['QUERY_STRING']) : ''; ?>&export=pdf" class="btn btn-danger">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </a>
            <a href="index.php?page=reports" class="btn btn-secondary">
                <i class="fas fa-sync me-2"></i>إعادة تعيين
            </a>
        </div>
    </div>
    
    <!-- نموذج التصفية -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>تصفية التقرير</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports">
                <input type="hidden" name="type" value="operations">
                
                <?php if (hasPermission('manage_companies') && isset($camps) && !empty($camps)): ?>
                <div class="col-md-4">
                    <label for="camp_id" class="form-label">الموقع</label>
                    <select name="camp_id" id="camp_id" class="form-select">
                        <option value="">جميع المواقع</option>
                        <?php foreach ($camps as $camp): ?>
                        <option value="<?php echo $camp['id']; ?>" <?php echo (isset($_GET['camp_id']) && $_GET['camp_id'] == $camp['id']) ? 'selected' : ''; ?>>
                            <?php echo $camp['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <div class="col-md-4">
                    <label for="driver_id" class="form-label">السائق</label>
                    <select name="driver_id" id="driver_id" class="form-select">
                        <option value="">جميع السائقين</option>
                        <?php foreach ($drivers as $driver): ?>
                        <option value="<?php echo $driver['id']; ?>" <?php echo (isset($_GET['driver_id']) && $_GET['driver_id'] == $driver['id']) ? 'selected' : ''; ?>>
                            <?php echo $driver['name']; ?> (<?php echo $driver['vehicle_number']; ?>)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="material_id" class="form-label">المادة</label>
                    <select name="material_id" id="material_id" class="form-select">
                        <option value="">جميع المواد</option>
                        <?php foreach ($materials as $material): ?>
                        <option value="<?php echo $material['id']; ?>" <?php echo (isset($_GET['material_id']) && $_GET['material_id'] == $material['id']) ? 'selected' : ''; ?>>
                            <?php echo $material['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="operation_type" class="form-label">نوع العملية</label>
                    <select name="operation_type" id="operation_type" class="form-select">
                        <option value="">جميع العمليات</option>
                        <option value="entry" <?php echo (isset($_GET['operation_type']) && $_GET['operation_type'] == 'entry') ? 'selected' : ''; ?>>دخول</option>
                        <option value="exit" <?php echo (isset($_GET['operation_type']) && $_GET['operation_type'] == 'exit') ? 'selected' : ''; ?>>خروج</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo isset($_GET['date_from']) ? $_GET['date_from'] : ''; ?>">
                </div>
                
                <div class="col-md-4">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo isset($_GET['date_to']) ? $_GET['date_to'] : ''; ?>">
                </div>
                
                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>تصفية
                    </button>
                    <a href="index.php?page=reports&type=operations" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i>إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- ملخص التقرير -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي العمليات</h5>
                    <p class="card-text display-6"><?php echo count($operations); ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">عمليات الدخول</h5>
                    <p class="card-text display-6"><?php echo $entryCount; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">عمليات الخروج</h5>
                    <p class="card-text display-6"><?php echo $exitCount; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي التكاليف</h5>
                    <p class="card-text display-6"><?php echo number_format($totalCost); ?> د.ع</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول العمليات -->
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة العمليات</h5>
            <span class="badge bg-primary"><?php echo count($operations); ?> عملية</span>
        </div>
        <div class="card-body">
            <?php if (empty($operations)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>لا توجد عمليات مسجلة تطابق معايير البحث
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>السائق</th>
                            <th>رقم السيارة</th>
                            <th>المادة</th>
                            <th>نوع العملية</th>
                            <th>الكمية</th>
                            <th>التكلفة</th>
                            <?php if (hasPermission('manage_companies')): ?>
                            <th>الموقع</th>
                            <?php endif; ?>
                            <th>التاريخ</th>
                            <th>المستخدم</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($operations as $index => $operation): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo $operation['driver_name']; ?></td>
                            <td><?php echo $operation['vehicle_number']; ?></td>
                            <td><?php echo $operation['material_name']; ?></td>
                            <td>
                                <?php if ($operation['operation_type'] == 'entry'): ?>
                                <span class="badge bg-success">دخول</span>
                                <?php else: ?>
                                <span class="badge bg-danger">خروج</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $operation['quantity']; ?></td>
                            <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                            <?php if (hasPermission('manage_companies')): ?>
                            <td><?php echo $operation['camp_name']; ?></td>
                            <?php endif; ?>
                            <td><?php echo date('Y-m-d H:i', strtotime($operation['created_at'])); ?></td>
                            <td><?php echo $operation['user_name']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th colspan="6">الإجمالي</th>
                            <th><?php echo number_format($totalCost); ?> د.ع</th>
                            <th colspan="<?php echo hasPermission('manage_companies') ? '3' : '2'; ?>"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>