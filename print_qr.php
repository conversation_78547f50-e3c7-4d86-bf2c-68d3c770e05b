<?php
/**
 * صفحة طباعة بطاقة QR للسائق
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من تسجيل دخول المستخدم
requireLogin();

// التحقق من وجود معرف السائق
if (!isset($_GET['driver_id']) || empty($_GET['driver_id'])) {
    die('معرف السائق غير صحيح');
}

$driverId = (int)$_GET['driver_id'];

// الحصول على بيانات السائق
$driverQuery = "SELECT d.*, c.name as camp_name 
               FROM drivers d 
               LEFT JOIN camps c ON d.camp_id = c.id 
               WHERE d.id = ?";
$driver = fetchRow($driverQuery, [$driverId]);

if (!$driver) {
    die('السائق غير موجود');
}

// التحقق من صلاحية الوصول إلى السائق
if (!hasPermission('manage_companies') && $driver['camp_id'] != $_SESSION['camp_id']) {
    die('ليس لديك صلاحية للوصول إلى هذا السائق');
}

// توليد رابط QR Code
$qrCodeUrl = '';
if (!empty($driver['qr_code'])) {
    $qrData = $driver['qr_code'];
    $qrCodeUrl = generateQRCode($qrData, 300);
} else {
    // إذا لم يكن هناك رمز QR، قم بإنشاء واحد
    $qrData = base64_encode(json_encode([
        'id' => $driver['id'],
        'name' => $driver['name'],
        'vehicle' => $driver['vehicle_number']
    ]));
    
    // تحديث السائق برمز QR
    updateData('drivers', ['qr_code' => $qrData], 'id = ?', [$driverId]);
    
    $qrCodeUrl = generateQRCode($qrData, 300);
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة QR - <?php echo htmlspecialchars($driver['name']); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .qr-card {
            width: 400px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .qr-card-header {
            background-color: #0d6efd;
            color: #fff;
            padding: 15px;
            text-align: center;
        }
        .qr-card-body {
            padding: 20px;
            text-align: center;
        }
        .qr-code-img {
            margin: 15px auto;
            max-width: 100%;
        }
        .driver-info {
            margin-top: 20px;
            text-align: right;
        }
        .driver-info table {
            width: 100%;
        }
        .driver-info th {
            width: 40%;
            text-align: right;
            padding: 5px;
        }
        .driver-info td {
            padding: 5px;
        }
        .card-footer {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 0.9em;
            border-top: 1px solid #dee2e6;
        }
        @media print {
            body {
                background-color: #fff;
                padding: 0;
            }
            .qr-card {
                box-shadow: none;
                width: 100%;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="no-print mb-3 text-center">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> طباعة البطاقة
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                إغلاق
            </button>
        </div>
        
        <div class="qr-card">
            <div class="qr-card-header">
                <h4><?php echo SITE_NAME; ?></h4>
                <h6>بطاقة تعريف سائق</h6>
            </div>
            
            <div class="qr-card-body">
                <div class="qr-code-img">
                    <img src="<?php echo $qrCodeUrl; ?>" alt="QR Code" class="img-fluid">
                </div>
                
                <div class="driver-info">
                    <table class="table table-bordered">
                        <tr>
                            <th>الاسم</th>
                            <td><?php echo htmlspecialchars($driver['name']); ?></td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td><?php echo htmlspecialchars($driver['phone'] ?: '-'); ?></td>
                        </tr>
                        <tr>
                            <th>نوع السيارة</th>
                            <td><?php echo htmlspecialchars($driver['vehicle_type'] ?: '-'); ?></td>
                        </tr>
                        <tr>
                            <th>رقم السيارة</th>
                            <td><?php echo htmlspecialchars($driver['vehicle_number']); ?></td>
                        </tr>
                        <?php if ($driver['camp_name']): ?>
                        <tr>
                            <th>الموقع</th>
                            <td><?php echo htmlspecialchars($driver['camp_name']); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
            
            <div class="card-footer">
                <p class="mb-0">تم إصدار هذه البطاقة بتاريخ: <?php echo date('Y-m-d'); ?></p>
                <p class="mb-0">الرقم التعريفي: <?php echo $driver['id']; ?></p>
            </div>
        </div>
    </div>
</body>
</html>