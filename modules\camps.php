<?php
/**
 * صفحة إدارة المواقع
 */

// التحقق من الصلاحيات
requirePermission('manage_camps');

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'list';

// معالجة نموذج إضافة/تعديل الموقع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_camp']) || isset($_POST['edit_camp'])) {
        // جمع بيانات النموذج
        $campData = [
            'company_id' => (int)$_POST['company_id'],
            'name' => sanitizeInput($_POST['name']),
            'location' => sanitizeInput($_POST['location']),
            'manager_name' => sanitizeInput($_POST['manager_name']),
            'phone' => sanitizeInput($_POST['phone'])
        ];
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($campData['name'])) {
            $errors[] = 'يرجى إدخال اسم الموقع';
        }
        
        if (empty($campData['company_id'])) {
            $errors[] = 'يرجى اختيار الشركة';
        }
        
        // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
        if (empty($errors)) {
            if (isset($_POST['add_camp'])) {
                // إضافة موقع جديد
                $campId = insertData('camps', $campData);
                
                if ($campId) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'add_camp',
                        'details' => json_encode([
                            'camp_id' => $campId,
                            'camp_name' => $campData['name'],
                            'company_id' => $campData['company_id']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المواقع مع رسالة نجاح
                    header('Location: index.php?page=camps&success=تم إضافة الموقع بنجاح');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء إضافة الموقع';
                }
            } else {
                // تحديث موقع موجود
                $campId = (int)$_POST['camp_id'];
                
                $updateResult = updateData('camps', $campData, 'id = ?', [$campId]);
                
                if ($updateResult) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'edit_camp',
                        'details' => json_encode([
                            'camp_id' => $campId,
                            'camp_name' => $campData['name'],
                            'company_id' => $campData['company_id']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المواقع مع رسالة نجاح
                    header('Location: index.php?page=camps&success=تم تحديث الموقع بنجاح');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء تحديث الموقع';
                }
            }
        }
    }
}

// عرض الصفحة المطلوبة
// معالجة الإجراءات المختلفة
switch ($action) {
    case 'add':
        // الحصول على قائمة الشركات
        $companiesQuery = "SELECT * FROM companies WHERE name = 'النرجس' ORDER BY name";
        $companies = fetchAll($companiesQuery);
        
        // عرض نموذج إضافة موقع جديد
        include 'templates/camps/add.php';
        break;
        
    case 'edit':
        // عرض نموذج تعديل موقع
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $campId = (int)$_GET['id'];
            
            // الحصول على بيانات الموقع
            $campQuery = "SELECT * FROM camps WHERE id = ?";
            $camp = fetchRow($campQuery, [$campId]);
            
            if ($camp) {
                // الحصول على قائمة الشركات
                $companiesQuery = "SELECT * FROM companies WHERE name = 'النرجس' ORDER BY name";
                $companies = fetchAll($companiesQuery);
                
                include 'templates/camps/edit.php';
            } else {
                echo '<div class="alert alert-danger">الموقع غير موجود</div>';
                header('Location: index.php?page=camps');
                exit;
            }
        } else {
            echo '<div class="alert alert-danger">معرف الموقع غير صحيح</div>';
            header('Location: index.php?page=camps');
            exit;
        }
        break;
        
    case 'view':
        // عرض تفاصيل الموقع
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $campId = (int)$_GET['id'];
            
            // الحصول على بيانات الموقع
            $campQuery = "SELECT c.*, co.name as company_name 
                          FROM camps c 
                          LEFT JOIN companies co ON c.company_id = co.id 
                          WHERE c.id = ?";
            $camp = fetchRow($campQuery, [$campId]);
            
            if ($camp) {
                // الحصول على إحصائيات الموقع
                // عدد السائقين
                $driversQuery = "SELECT COUNT(*) as count FROM drivers WHERE camp_id = ?";
                $driversResult = fetchRow($driversQuery, [$campId]);
                $driverCount = $driversResult ? $driversResult['count'] : 0;
                
                // عدد العمليات والتكلفة الإجمالية
                $operationsQuery = "SELECT COUNT(*) as count, SUM(cost) as total_cost 
                                   FROM operations 
                                   WHERE camp_id = ?";
                $operationsResult = fetchRow($operationsQuery, [$campId]);
                $operationCount = $operationsResult ? $operationsResult['count'] : 0;
                $totalCost = $operationsResult ? $operationsResult['total_cost'] : 0;
                
                include 'templates/camps/view.php';
            } else {
                echo '<div class="alert alert-danger">الموقع غير موجود</div>';
                header('Location: index.php?page=camps');
                exit;
            }
        } else {
            echo '<div class="alert alert-danger">معرف الموقع غير صحيح</div>';
            header('Location: index.php?page=camps');
            exit;
        }
        break;
        
    case 'delete':
        // حذف موقع
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $campId = (int)$_GET['id'];
            
            // التحقق من وجود الموقع
            $campQuery = "SELECT * FROM camps WHERE id = ?";
            $camp = fetchRow($campQuery, [$campId]);
            
            if ($camp) {
                // التحقق من عدم وجود سائقين أو عمليات مرتبطة بالموقع
                $driversQuery = "SELECT COUNT(*) as count FROM drivers WHERE camp_id = ?";
                $driversResult = fetchRow($driversQuery, [$campId]);
                $driverCount = $driversResult ? $driversResult['count'] : 0;
                
                $operationsQuery = "SELECT COUNT(*) as count FROM operations WHERE camp_id = ?";
                $operationsResult = fetchRow($operationsQuery, [$campId]);
                $operationCount = $operationsResult ? $operationsResult['count'] : 0;
                
                if ($driverCount > 0 || $operationCount > 0) {
                    // لا يمكن حذف الموقع لأنه مرتبط بسائقين أو عمليات
                    header('Location: index.php?page=camps&error=لا يمكن حذف الموقع لأنه مرتبط بسائقين أو عمليات');
                    exit;
                }
                
                // حذف الموقع
                $deleteResult = deleteData('camps', 'id = ?', [$campId]);
                
                if ($deleteResult) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'delete_camp',
                        'details' => json_encode([
                            'camp_id' => $campId,
                            'camp_name' => $camp['name']
                        ]),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    // إعادة توجيه إلى قائمة المواقع مع رسالة نجاح
                    header('Location: index.php?page=camps&success=تم حذف الموقع بنجاح');
                    exit;
                } else {
                    header('Location: index.php?page=camps&error=حدث خطأ أثناء حذف الموقع');
                    exit;
                }
            } else {
                header('Location: index.php?page=camps&error=الموقع غير موجود');
                exit;
            }
        } else {
            header('Location: index.php?page=camps&error=معرف الموقع غير صحيح');
            exit;
        }
        break;
        
    default:
        // عرض قائمة المواقع
        $campsQuery = "SELECT c.*, co.name as company_name, 
                      (SELECT COUNT(*) FROM drivers WHERE camp_id = c.id) as driver_count,
                      (SELECT COUNT(*) FROM operations WHERE camp_id = c.id) as operation_count
                      FROM camps c 
                      LEFT JOIN companies co ON c.company_id = co.id 
                      ORDER BY c.name";
        $camps = fetchAll($campsQuery);
        
        include 'templates/camps/list.php';
        break;
}
?>