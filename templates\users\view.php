<?php
/**
 * قالب عرض تفاصيل المستخدم
 */
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3"><i class="fas fa-user me-2"></i>تفاصيل المستخدم</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php?page=dashboard">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=users">المستخدمين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">عرض مستخدم</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات المستخدم</h5>
                    <div>
                        <a href="index.php?page=users" class="btn btn-secondary btn-sm me-2">
                            <i class="fas fa-arrow-right me-1"></i> العودة
                        </a>
                        <a href="index.php?page=users&action=edit&id=<?php echo $user['id']; ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i> تعديل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%">الاسم</th>
                                    <td><?php echo htmlspecialchars($user['name']); ?></td>
                                </tr>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                </tr>
                                <tr>
                                    <th>البريد الإلكتروني</th>
                                    <td><?php echo htmlspecialchars($user['email'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <th>الصلاحية</th>
                                    <td><?php echo getUserRoleText($user['role']); ?></td>
                                </tr>
                                <tr>
                                    <th>الشركة</th>
                                    <td><?php echo htmlspecialchars($user['company_name'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <th>الموقع</th>
                                    <td><?php echo htmlspecialchars($user['camp_name'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنشاء</th>
                                    <td><?php echo formatDate($user['created_at']); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!empty($logs)): ?>
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>سجل النشاط</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النشاط</th>
                                    <th>التفاصيل</th>
                                    <th>عنوان IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td><?php echo formatDateTime($log['timestamp']); ?></td>
                                    <td><?php echo getActivityText($log['action']); ?></td>
                                    <td>
                                        <?php 
                                        $details = json_decode($log['details'], true);
                                        if ($details) {
                                            foreach ($details as $key => $value) {
                                                echo '<div><strong>' . htmlspecialchars($key) . ':</strong> ' . htmlspecialchars($value) . '</div>';
                                            }
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
/**
 * دالة لتحويل نوع النشاط إلى نص مقروء
 * @param string $action نوع النشاط
 * @return string النص المقروء
 */
function getActivityText($action) {
    switch ($action) {
        case 'login':
            return 'تسجيل الدخول';
        case 'logout':
            return 'تسجيل الخروج';
        case 'add_user':
            return 'إضافة مستخدم';
        case 'edit_user':
            return 'تعديل مستخدم';
        case 'add_camp':
            return 'إضافة موقع';
        case 'edit_camp':
            return 'تعديل موقع';
        case 'add_driver':
            return 'إضافة سائق';
        case 'edit_driver':
            return 'تعديل سائق';
        case 'add_operation':
            return 'إضافة عملية';
        case 'edit_operation':
            return 'تعديل عملية';
        default:
            return $action;
    }
}
?>