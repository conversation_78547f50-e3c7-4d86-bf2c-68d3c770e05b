<?php
/**
 * قالب عرض تفاصيل الموقع
 */
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3"><i class="fas fa-map-marker-alt me-2"></i><?php echo $camp['name']; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php?page=dashboard">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=camps">إدارة المواقع</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تفاصيل الموقع</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الموقع</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">اسم الموقع</th>
                            <td><?php echo $camp['name']; ?></td>
                        </tr>
                        <tr>
                            <th>الشركة</th>
                            <td>
                                <a href="index.php?page=companies&action=view&id=<?php echo $camp['company_id']; ?>">
                                    <?php echo $camp['company_name']; ?>
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>الموقع</th>
                            <td><?php echo $camp['location'] ?: '-'; ?></td>
                        </tr>
                        <tr>
                            <th>المسؤول</th>
                            <td><?php echo $camp['manager_name'] ?: '-'; ?></td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td><?php echo $camp['phone'] ?: '-'; ?></td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <a href="index.php?page=camps&action=edit&id=<?php echo $camp['id']; ?>" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>تعديل البيانات
                        </a>
                        <a href="index.php?page=camps" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الموقع -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>إحصائيات الموقع</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-3">
                                    <h3 class="mb-0"><?php echo $stats['drivers_count']; ?></h3>
                                    <p class="mb-0 text-muted">السائقين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-3">
                                    <h3 class="mb-0"><?php echo $stats['operations_count']; ?></h3>
                                    <p class="mb-0 text-muted">العمليات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body py-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_cost'] ?: 0); ?> د.ع</h3>
                                    <p class="mb-0 text-muted">إجمالي التكلفة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <!-- قائمة السائقين -->
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>السائقين في الموقع</h5>
                    <div>
                        <a href="index.php?page=drivers&camp_id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-list me-1"></i>عرض الكل
                        </a>
                        <a href="index.php?page=drivers&action=add&camp_id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-success">
                            <i class="fas fa-plus me-1"></i>إضافة سائق
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php 
                    // الحصول على قائمة السائقين في الموقع
                    $driversQuery = "SELECT * FROM drivers WHERE camp_id = ? ORDER BY name LIMIT 5";
                    $drivers = fetchAll($driversQuery, [$camp['id']]);
                    ?>
                    
                    <?php if (empty($drivers)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>لا يوجد سائقين مسجلين في هذا الموقع
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم السائق</th>
                                    <th>رقم السيارة</th>
                                    <th>رقم الهاتف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($drivers as $index => $driver): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo $driver['name']; ?></td>
                                    <td><?php echo $driver['vehicle_number']; ?></td>
                                    <td><?php echo $driver['phone'] ?: '-'; ?></td>
                                    <td>
                                        <a href="index.php?page=drivers&action=view&id=<?php echo $driver['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- آخر العمليات -->
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>آخر العمليات</h5>
                    <a href="index.php?page=operations&camp_id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-list me-1"></i>عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php 
                    // الحصول على آخر العمليات في الموقع
                    $operationsQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name 
                                      FROM operations o 
                                      LEFT JOIN drivers d ON o.driver_id = d.id 
                                      LEFT JOIN materials m ON o.material_id = m.id 
                                      WHERE o.camp_id = ? 
                                      ORDER BY o.created_at DESC LIMIT 5";
                    $operations = fetchAll($operationsQuery, [$camp['id']]);
                    ?>
                    
                    <?php if (empty($operations)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>لا توجد عمليات مسجلة لهذا الموقع
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>السائق</th>
                                    <th>المادة</th>
                                    <th>نوع العملية</th>
                                    <th>الكمية</th>
                                    <th>التكلفة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($operations as $index => $operation): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo $operation['driver_name']; ?></td>
                                    <td><?php echo $operation['material_name']; ?></td>
                                    <td>
                                        <?php if ($operation['operation_type'] == 'entry'): ?>
                                        <span class="badge bg-success">دخول</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">خروج</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $operation['quantity']; ?></td>
                                    <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($operation['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>