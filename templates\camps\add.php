<?php
/**
 * قالب إضافة موقع جديد
 */
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="h3"><i class="fas fa-plus-circle me-2"></i>إضافة موقع جديد</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php?page=dashboard">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=camps">إدارة المواقع</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إضافة موقع جديد</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>بيانات الموقع</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    
                    <form method="post" action="index.php?page=camps&action=add">
                        <div class="mb-3">
                            <label for="company_id" class="form-label">الشركة <span class="text-danger">*</span></label>
                            <select class="form-select" id="company_id" name="company_id" required>
                                <option value="">اختر الشركة</option>
                                <?php foreach ($companies as $company): ?>
                                <option value="<?php echo $company['id']; ?>" <?php echo (isset($_POST['company_id']) && $_POST['company_id'] == $company['id']) ? 'selected' : (isset($_GET['company_id']) && $_GET['company_id'] == $company['id'] ? 'selected' : ''); ?>>
                                    <?php echo $company['name']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الموقع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? $_POST['name'] : ''; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">الموقع</label>
                            <input type="text" class="form-control" id="location" name="location" value="<?php echo isset($_POST['location']) ? $_POST['location'] : ''; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="manager_name" class="form-label">اسم المسؤول</label>
                            <input type="text" class="form-control" id="manager_name" name="manager_name" value="<?php echo isset($_POST['manager_name']) ? $_POST['manager_name'] : ''; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? $_POST['phone'] : ''; ?>">
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" name="add_camp" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ
                            </button>
                            <a href="index.php?page=camps" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>