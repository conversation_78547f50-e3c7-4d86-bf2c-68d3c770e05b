<?php
/**
 * صفحة إدارة السائقين
 */

// التحقق من الصلاحيات
requirePermission('manage_drivers');

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'list';

// معالجة نموذج إضافة/تعديل السائق
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_driver']) || isset($_POST['edit_driver'])) {
        // جمع بيانات النموذج
        $driverData = [
            'name' => sanitizeInput($_POST['name']),
            'phone' => sanitizeInput($_POST['phone']),
            'vehicle_type' => sanitizeInput($_POST['vehicle_type']),
            'vehicle_number' => sanitizeInput($_POST['vehicle_number']),
            'notes' => sanitizeInput($_POST['notes'])
        ];
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($driverData['name'])) {
            $errors[] = 'يرجى إدخال اسم السائق';
        }
        
        if (empty($driverData['vehicle_number'])) {
            $errors[] = 'يرجى إدخال رقم السيارة';
        }
        
        // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
        if (empty($errors)) {
            // تحديد الكمب الحالي
            if (hasPermission('manage_companies')) {
                $driverData['camp_id'] = isset($_POST['camp_id']) ? (int)$_POST['camp_id'] : null;
            } else {
                $driverData['camp_id'] = $_SESSION['camp_id'];
            }
            
            $driverData['created_by'] = $_SESSION['user_id'];
            
            if (isset($_POST['add_driver'])) {
                // إضافة سائق جديد
                $driverId = insertData('drivers', $driverData);
                
                if ($driverId) {
                    // توليد رمز QR للسائق
                    $qrData = json_encode([
                        'id' => $driverId,
                        'name' => $driverData['name'],
                        'vehicle' => $driverData['vehicle_number']
                    ]);
                    
                    $qrCode = base64_encode($qrData);
                    
                    // تحديث السائق برمز QR
                    updateData('drivers', ['qr_code' => $qrCode], 'id = ?', [$driverId]);
                    
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'add_driver',
                        'details' => 'تمت إضافة سائق جديد: ' . $driverData['name'],
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    $_SESSION['success'] = 'تمت إضافة السائق بنجاح';
                    header('Location: index.php?page=drivers');
                    exit;
                } else {
                    $errors[] = 'حدث خطأ أثناء إضافة السائق';
                }
            } elseif (isset($_POST['edit_driver'])) {
                // تعديل سائق موجود
                $driverId = (int)$_POST['driver_id'];
                
                // التحقق من وجود السائق
                $driverQuery = "SELECT * FROM drivers WHERE id = ?";
                $driver = fetchRow($driverQuery, [$driverId]);
                
                if ($driver) {
                    // تحديث بيانات السائق
                    $updated = updateData('drivers', $driverData, 'id = ?', [$driverId]);
                    
                    if ($updated !== false) {
                        // تسجيل النشاط
                        $logData = [
                            'user_id' => $_SESSION['user_id'],
                            'action' => 'edit_driver',
                            'details' => 'تم تعديل بيانات السائق: ' . $driverData['name'],
                            'ip_address' => $_SERVER['REMOTE_ADDR']
                        ];
                        insertData('activity_logs', $logData);
                        
                        $_SESSION['success'] = 'تم تعديل بيانات السائق بنجاح';
                        header('Location: index.php?page=drivers');
                        exit;
                    } else {
                        $errors[] = 'حدث خطأ أثناء تعديل بيانات السائق';
                    }
                } else {
                    $errors[] = 'السائق غير موجود';
                }
            }
        }
    } elseif (isset($_POST['delete_driver'])) {
        // حذف سائق
        $driverId = (int)$_POST['driver_id'];
        
        // التحقق من وجود السائق
        $driverQuery = "SELECT * FROM drivers WHERE id = ?";
        $driver = fetchRow($driverQuery, [$driverId]);
        
        if ($driver) {
            // التحقق من وجود عمليات مرتبطة بالسائق
            $operationsQuery = "SELECT COUNT(*) as count FROM operations WHERE driver_id = ?";
            $operationsCount = fetchRow($operationsQuery, [$driverId])['count'];
            
            if ($operationsCount > 0) {
                $_SESSION['error'] = 'لا يمكن حذف السائق لوجود عمليات مرتبطة به';
            } else {
                // حذف السائق
                $deleted = deleteData('drivers', 'id = ?', [$driverId]);
                
                if ($deleted) {
                    // تسجيل النشاط
                    $logData = [
                        'user_id' => $_SESSION['user_id'],
                        'action' => 'delete_driver',
                        'details' => 'تم حذف السائق: ' . $driver['name'],
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ];
                    insertData('activity_logs', $logData);
                    
                    $_SESSION['success'] = 'تم حذف السائق بنجاح';
                } else {
                    $_SESSION['error'] = 'حدث خطأ أثناء حذف السائق';
                }
            }
            
            header('Location: index.php?page=drivers');
            exit;
        } else {
            $_SESSION['error'] = 'السائق غير موجود';
            header('Location: index.php?page=drivers');
            exit;
        }
    }
}

// عرض قائمة السائقين
if ($action === 'list') {
    // الحصول على قائمة السائقين
    $driversQuery = "SELECT d.*, c.name as camp_name 
                     FROM drivers d 
                     LEFT JOIN camps c ON d.camp_id = c.id";
    
    $params = [];
    
    // تصفية حسب الكمب إذا لم يكن المستخدم مدير النظام
    if (!hasPermission('manage_companies')) {
        $driversQuery .= " WHERE d.camp_id = ?";
        $params[] = $_SESSION['camp_id'];
    }
    
    $driversQuery .= " ORDER BY d.name ASC";
    $drivers = fetchAll($driversQuery, $params);
    
    // الحصول على قائمة الكمبات للفلتر
    $campsQuery = "SELECT * FROM camps ORDER BY name ASC";
    $camps = fetchAll($campsQuery);
    
    // عرض الصفحة
    ?>
    <div class="drivers-list">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قائمة السائقين</h5>
                <a href="index.php?page=drivers&action=add" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> إضافة سائق جديد
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>نوع السيارة</th>
                                <th>رقم السيارة</th>
                                <?php if (hasPermission('manage_companies')): ?>
                                <th>الكمب</th>
                                <?php endif; ?>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($drivers)): ?>
                            <tr>
                                <td colspan="<?php echo hasPermission('manage_companies') ? 7 : 6; ?>" class="text-center">لا يوجد سائقين</td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($drivers as $driver): ?>
                                <tr>
                                    <td><?php echo $driver['id']; ?></td>
                                    <td><?php echo htmlspecialchars($driver['name']); ?></td>
                                    <td><?php echo htmlspecialchars($driver['phone']); ?></td>
                                    <td><?php echo htmlspecialchars($driver['vehicle_type']); ?></td>
                                    <td><?php echo htmlspecialchars($driver['vehicle_number']); ?></td>
                                    <?php if (hasPermission('manage_companies')): ?>
                                    <td><?php echo htmlspecialchars($driver['camp_name'] ?? 'غير محدد'); ?></td>
                                    <?php endif; ?>
                                    <td class="action-buttons">
                                        <a href="index.php?page=drivers&action=view&id=<?php echo $driver['id']; ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?page=drivers&action=edit&id=<?php echo $driver['id']; ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $driver['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <a href="javascript:void(0);" onclick="printQRCard(<?php echo $driver['id']; ?>)" class="btn btn-primary btn-sm" title="طباعة بطاقة QR">
                                            <i class="fas fa-qrcode"></i>
                                        </a>
                                        
                                        <!-- نافذة تأكيد الحذف -->
                                        <div class="modal fade" id="deleteModal<?php echo $driver['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $driver['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo $driver['id']; ?>">تأكيد الحذف</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        هل أنت متأكد من حذف السائق: <strong><?php echo htmlspecialchars($driver['name']); ?></strong>؟
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                        <form method="post" action="">
                                                            <input type="hidden" name="driver_id" value="<?php echo $driver['id']; ?>">
                                                            <button type="submit" name="delete_driver" class="btn btn-danger">حذف</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- سكريبت طباعة بطاقة QR -->
    <script>
        function printQRCard(driverId) {
            const printWindow = window.open(`print_qr.php?driver_id=${driverId}`, '_blank', 'width=800,height=600');
            printWindow.addEventListener('load', function() {
                printWindow.print();
            });
        }
    </script>
    <?php
} elseif ($action === 'add') {
    // عرض نموذج إضافة سائق جديد
    
    // الحصول على قائمة الكمبات
    $campsQuery = "SELECT * FROM camps ORDER BY name ASC";
    $camps = fetchAll($campsQuery);
    ?>
    <div class="add-driver">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إضافة سائق جديد</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم السائق <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vehicle_type" class="form-label">نوع السيارة</label>
                            <input type="text" class="form-control" id="vehicle_type" name="vehicle_type">
                        </div>
                        <div class="col-md-6">
                            <label for="vehicle_number" class="form-label">رقم السيارة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="vehicle_number" name="vehicle_number" required>
                        </div>
                    </div>
                    
                    <?php if (hasPermission('manage_companies') && !empty($camps)): ?>
                    <div class="mb-3">
                        <label for="camp_id" class="form-label">الكمب</label>
                        <select class="form-select" id="camp_id" name="camp_id">
                            <option value="">-- اختر الكمب --</option>
                            <?php foreach ($camps as $camp): ?>
                            <option value="<?php echo $camp['id']; ?>"><?php echo htmlspecialchars($camp['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php?page=drivers" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" name="add_driver" class="btn btn-primary">إضافة السائق</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
} elseif ($action === 'edit') {
    // عرض نموذج تعديل سائق
    $driverId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    // التحقق من وجود السائق
    $driverQuery = "SELECT * FROM drivers WHERE id = ?";
    $driver = fetchRow($driverQuery, [$driverId]);
    
    if (!$driver) {
        $_SESSION['error'] = 'السائق غير موجود';
        header('Location: index.php?page=drivers');
        exit;
    }
    
    // التحقق من صلاحية الوصول إلى السائق
    if (!hasPermission('manage_companies') && $driver['camp_id'] != $_SESSION['camp_id']) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول إلى هذا السائق';
        header('Location: index.php?page=drivers');
        exit;
    }
    
    // الحصول على قائمة الكمبات
    $campsQuery = "SELECT * FROM camps ORDER BY name ASC";
    $camps = fetchAll($campsQuery);
    ?>
    <div class="edit-driver">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل بيانات السائق</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <input type="hidden" name="driver_id" value="<?php echo $driver['id']; ?>">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم السائق <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($driver['name']); ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($driver['phone']); ?>">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vehicle_type" class="form-label">نوع السيارة</label>
                            <input type="text" class="form-control" id="vehicle_type" name="vehicle_type" value="<?php echo htmlspecialchars($driver['vehicle_type']); ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="vehicle_number" class="form-label">رقم السيارة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="vehicle_number" name="vehicle_number" value="<?php echo htmlspecialchars($driver['vehicle_number']); ?>" required>
                        </div>
                    </div>
                    
                    <?php if (hasPermission('manage_companies') && !empty($camps)): ?>
                    <div class="mb-3">
                        <label for="camp_id" class="form-label">الكمب</label>
                        <select class="form-select" id="camp_id" name="camp_id">
                            <option value="">-- اختر الكمب --</option>
                            <?php foreach ($camps as $camp): ?>
                            <option value="<?php echo $camp['id']; ?>" <?php echo ($driver['camp_id'] == $camp['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($camp['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($driver['notes']); ?></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php?page=drivers" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" name="edit_driver" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
} elseif ($action === 'view') {
    // عرض تفاصيل السائق
    $driverId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    // التحقق من وجود السائق
    $driverQuery = "SELECT d.*, c.name as camp_name 
                   FROM drivers d 
                   LEFT JOIN camps c ON d.camp_id = c.id 
                   WHERE d.id = ?";
    $driver = fetchRow($driverQuery, [$driverId]);
    
    if (!$driver) {
        $_SESSION['error'] = 'السائق غير موجود';
        header('Location: index.php?page=drivers');
        exit;
    }
    
    // التحقق من صلاحية الوصول إلى السائق
    if (!hasPermission('manage_companies') && $driver['camp_id'] != $_SESSION['camp_id']) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول إلى هذا السائق';
        header('Location: index.php?page=drivers');
        exit;
    }
    
    // الحصول على عدد العمليات للسائق
    $operationsQuery = "SELECT 
                        COUNT(*) as total_operations,
                        SUM(CASE WHEN operation_type = 'entry' THEN 1 ELSE 0 END) as entry_operations,
                        SUM(CASE WHEN operation_type = 'exit' THEN 1 ELSE 0 END) as exit_operations
                        FROM operations 
                        WHERE driver_id = ?";
    $operationsStats = fetchRow($operationsQuery, [$driverId]);
    
    // توليد رابط QR Code
    $qrCodeUrl = '';
    if (!empty($driver['qr_code'])) {
        $qrData = base64_encode(json_encode([
            'id' => $driver['id'],
            'name' => $driver['name'],
            'vehicle' => $driver['vehicle_number']
        ]));
        $qrCodeUrl = generateQRCode($qrData);
    }
    ?>
    <div class="view-driver">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">بيانات السائق</h5>
                <div>
                    <a href="index.php?page=drivers&action=edit&id=<?php echo $driver['id']; ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="index.php?page=drivers" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">الاسم</th>
                                <td><?php echo htmlspecialchars($driver['name']); ?></td>
                            </tr>
                            <tr>
                                <th>رقم الهاتف</th>
                                <td><?php echo htmlspecialchars($driver['phone'] ?: 'غير متوفر'); ?></td>
                            </tr>
                            <tr>
                                <th>نوع السيارة</th>
                                <td><?php echo htmlspecialchars($driver['vehicle_type'] ?: 'غير متوفر'); ?></td>
                            </tr>
                            <tr>
                                <th>رقم السيارة</th>
                                <td><?php echo htmlspecialchars($driver['vehicle_number']); ?></td>
                            </tr>
                            <?php if (hasPermission('manage_companies')): ?>
                            <tr>
                                <th>الكمب</th>
                                <td><?php echo htmlspecialchars($driver['camp_name'] ?: 'غير محدد'); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>تاريخ الإضافة</th>
                                <td><?php echo date('Y-m-d', strtotime($driver['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <th>ملاحظات</th>
                                <td><?php echo nl2br(htmlspecialchars($driver['notes'] ?: 'لا توجد ملاحظات')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="qr-code-container p-3 border rounded mb-3">
                            <h6>رمز QR</h6>
                            <?php if ($qrCodeUrl): ?>
                            <img src="<?php echo $qrCodeUrl; ?>" alt="QR Code" class="img-fluid mb-2">
                            <div>
                                <button onclick="printQRCard(<?php echo $driver['id']; ?>)" class="btn btn-primary btn-sm">
                                    <i class="fas fa-print me-1"></i> طباعة البطاقة
                                </button>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-warning">رمز QR غير متوفر</div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="stats-container p-3 border rounded">
                            <h6>إحصائيات العمليات</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-value"><?php echo number_format($operationsStats['total_operations']); ?></div>
                                        <div class="stat-label">إجمالي</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item text-success">
                                        <div class="stat-value"><?php echo number_format($operationsStats['entry_operations']); ?></div>
                                        <div class="stat-label">دخول</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item text-danger">
                                        <div class="stat-value"><?php echo number_format($operationsStats['exit_operations']); ?></div>
                                        <div class="stat-label">خروج</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- سكريبت طباعة بطاقة QR -->
    <script>
        function printQRCard(driverId) {
            const printWindow = window.open(`print_qr.php?driver_id=${driverId}`, '_blank', 'width=800,height=600');
            printWindow.addEventListener('load', function() {
                printWindow.print();
            });
        }
    </script>
    <?php
}
?>