<?php
/**
 * قالب تقرير السائقين
 */
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-users me-2"></i>تقرير السائقين</h2>
        <div>
            <a href="index.php?page=reports&type=drivers<?php echo isset($_SERVER['QUERY_STRING']) ? '&' . str_replace('page=reports&type=drivers&', '', $_SERVER['QUERY_STRING']) : ''; ?>&export=excel" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i>تصدير Excel
            </a>
            <a href="index.php?page=reports&type=drivers<?php echo isset($_SERVER['QUERY_STRING']) ? '&' . str_replace('page=reports&type=drivers&', '', $_SERVER['QUERY_STRING']) : ''; ?>&export=pdf" class="btn btn-danger">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </a>
            <a href="index.php?page=reports" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
            </a>
        </div>
    </div>
    
    <!-- نموذج التصفية -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>تصفية التقرير</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports">
                <input type="hidden" name="type" value="drivers">
                
                <?php if (hasPermission('manage_companies') && isset($camps) && !empty($camps)): ?>
                <div class="col-md-4">
                    <label for="camp_id" class="form-label">الموقع</label>
                    <select name="camp_id" id="camp_id" class="form-select">
                        <option value="">جميع المواقع</option>
                        <?php foreach ($camps as $camp): ?>
                        <option value="<?php echo $camp['id']; ?>" <?php echo (isset($_GET['camp_id']) && $_GET['camp_id'] == $camp['id']) ? 'selected' : ''; ?>>
                            <?php echo $camp['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>تصفية
                    </button>
                    <a href="index.php?page=reports&type=drivers" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i>إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- جدول السائقين -->
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة السائقين</h5>
            <span class="badge bg-primary"><?php echo count($drivers); ?> سائق</span>
        </div>
        <div class="card-body">
            <?php if (empty($drivers)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>لا يوجد سائقين مسجلين تطابق معايير البحث
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم السائق</th>
                            <th>رقم الهاتف</th>
                            <th>نوع السيارة</th>
                            <th>رقم السيارة</th>
                            <?php if (hasPermission('manage_companies')): ?>
                            <th>الموقع</th>
                            <?php endif; ?>
                            <th>عدد العمليات</th>
                            <th>إجمالي التكاليف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($drivers as $index => $driver): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo $driver['name']; ?></td>
                            <td><?php echo $driver['phone'] ?: '-'; ?></td>
                            <td><?php echo $driver['vehicle_type'] ?: '-'; ?></td>
                            <td><?php echo $driver['vehicle_number']; ?></td>
                            <?php if (hasPermission('manage_companies')): ?>
                            <td><?php echo $driver['camp_name']; ?></td>
                            <?php endif; ?>
                            <td><?php echo $driver['operations_count']; ?></td>
                            <td><?php echo number_format($driver['total_cost'] ?: 0); ?> د.ع</td>
                            <td>
                                <a href="index.php?page=operations&driver_id=<?php echo $driver['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-exchange-alt"></i>
                                </a>
                                <a href="index.php?page=drivers&action=view&id=<?php echo $driver['id']; ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="print_qr.php?driver_id=<?php echo $driver['id']; ?>" class="btn btn-sm btn-success" target="_blank">
                                    <i class="fas fa-qrcode"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>