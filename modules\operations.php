<?php
/**
 * صفحة إدارة العمليات (دخول وخروج السيارات)
 */

// التحقق من الصلاحيات
requirePermission('manage_operations');

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'list';

// معالجة نموذج إضافة عملية جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_operation'])) {
        // جمع بيانات النموذج
        $operationData = [
            'driver_id' => (int)$_POST['driver_id'],
            'material_id' => (int)$_POST['material_id'],
            'operation_type' => sanitizeInput($_POST['operation_type']),
            'quantity' => (float)$_POST['quantity'],
            'notes' => sanitizeInput($_POST['notes'])
        ];
        
        // التحقق من البيانات
        $errors = [];
        
        if (empty($operationData['driver_id'])) {
            $errors[] = 'يرجى اختيار السائق';
        }
        
        if (empty($operationData['material_id'])) {
            $errors[] = 'يرجى اختيار المادة';
        }
        
        if (empty($operationData['operation_type'])) {
            $errors[] = 'يرجى تحديد نوع العملية';
        }
        
        // إذا لم تكن هناك أخطاء، قم بحفظ البيانات
        if (empty($errors)) {
            // الحصول على سعر المادة
            $materialQuery = "SELECT price FROM materials WHERE id = ?";
            $material = fetchRow($materialQuery, [$operationData['material_id']]);
            
            // حساب التكلفة
            $operationData['cost'] = $material['price'] * $operationData['quantity'];
            
            // تحديد الموقع الحالي
            if (hasPermission('manage_companies')) {
                $operationData['camp_id'] = isset($_POST['camp_id']) ? (int)$_POST['camp_id'] : null;
            } else {
                $operationData['camp_id'] = $_SESSION['camp_id'];
            }
            
            $operationData['created_by'] = $_SESSION['user_id'];
            
            // إضافة عملية جديدة
            $operationId = insertData('operations', $operationData);
            
            if ($operationId) {
                // تسجيل النشاط
                $logData = [
                    'user_id' => $_SESSION['user_id'],
                    'action' => 'add_operation',
                    'details' => json_encode([
                        'operation_id' => $operationId,
                        'driver_name' => getDriverName($operationData['driver_id']),
                        'operation_type' => $operationData['operation_type'],
                        'cost' => $operationData['cost']
                    ]),
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ];
                insertData('activity_logs', $logData);
                
                // إعادة توجيه إلى قائمة العمليات مع رسالة نجاح
                header('Location: index.php?page=operations&success=تم إضافة العملية بنجاح');
                exit;
            } else {
                $errors[] = 'حدث خطأ أثناء إضافة العملية';
            }
        }
    }
}

// عرض الصفحة المطلوبة
switch ($action) {
    case 'add':
        // عرض نموذج إضافة عملية جديدة
        include 'templates/operations/add.php';
        break;
        
    case 'scan_qr':
        // عرض صفحة مسح QR Code
        include 'templates/operations/scan_qr.php';
        break;
        
    case 'process_qr':
        // معالجة بيانات QR Code
        if (isset($_POST['qr_data']) && !empty($_POST['qr_data'])) {
            $qrData = $_POST['qr_data'];
            
            try {
                // فك تشفير بيانات QR Code
                $decodedData = json_decode(base64_decode($qrData), true);
                
                if (isset($decodedData['id'])) {
                    $driverId = $decodedData['id'];
                    
                    // التحقق من وجود السائق
                    $driverQuery = "SELECT * FROM drivers WHERE id = ?";
                    $driver = fetchRow($driverQuery, [$driverId]);
                    
                    if ($driver) {
                        // عرض نموذج إضافة عملية مع بيانات السائق
                        $_GET['driver_id'] = $driverId;
                        include 'templates/operations/add.php';
                    } else {
                        echo '<div class="alert alert-danger">السائق غير موجود</div>';
                        include 'templates/operations/scan_qr.php';
                    }
                } else {
                    echo '<div class="alert alert-danger">بيانات QR Code غير صحيحة</div>';
                    include 'templates/operations/scan_qr.php';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">حدث خطأ أثناء معالجة بيانات QR Code</div>';
                include 'templates/operations/scan_qr.php';
            }
        } else {
            echo '<div class="alert alert-danger">لم يتم توفير بيانات QR Code</div>';
            include 'templates/operations/scan_qr.php';
        }
        break;
        
    case 'view':
        // عرض تفاصيل عملية
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $operationId = (int)$_GET['id'];
            
            // الحصول على بيانات العملية
            $operationQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name, c.name as camp_name, u.name as user_name 
                               FROM operations o 
                               LEFT JOIN drivers d ON o.driver_id = d.id 
                               LEFT JOIN materials m ON o.material_id = m.id 
                               LEFT JOIN camps c ON o.camp_id = c.id 
                               LEFT JOIN users u ON o.created_by = u.id 
                               WHERE o.id = ?";
            
            // إضافة شرط الكمب إذا لم يكن المستخدم مدير النظام
            if (!hasPermission('manage_companies')) {
                $operationQuery .= " AND o.camp_id = ?";
                $operation = fetchRow($operationQuery, [$operationId, $_SESSION['camp_id']]);
            } else {
                $operation = fetchRow($operationQuery, [$operationId]);
            }
            
            if ($operation) {
                include 'templates/operations/view.php';
            } else {
                echo '<div class="alert alert-danger">العملية غير موجودة أو ليس لديك صلاحية للوصول إليها</div>';
                include 'templates/operations/list.php';
            }
        } else {
            echo '<div class="alert alert-danger">معرف العملية غير صحيح</div>';
            include 'templates/operations/list.php';
        }
        break;
        
    default:
        // عرض قائمة العمليات
        // تحديد معايير التصفية
        $filters = [];
        $params = [];
        
        // تصفية حسب الكمب
        if (isset($_GET['camp_id']) && !empty($_GET['camp_id']) && hasPermission('manage_companies')) {
            $filters[] = "o.camp_id = ?";
            $params[] = (int)$_GET['camp_id'];
        } elseif (!hasPermission('manage_companies')) {
            $filters[] = "o.camp_id = ?";
            $params[] = $_SESSION['camp_id'];
        }
        
        // تصفية حسب السائق
        if (isset($_GET['driver_id']) && !empty($_GET['driver_id'])) {
            $filters[] = "o.driver_id = ?";
            $params[] = (int)$_GET['driver_id'];
        }
        
        // تصفية حسب المادة
        if (isset($_GET['material_id']) && !empty($_GET['material_id'])) {
            $filters[] = "o.material_id = ?";
            $params[] = (int)$_GET['material_id'];
        }
        
        // تصفية حسب نوع العملية
        if (isset($_GET['operation_type']) && !empty($_GET['operation_type'])) {
            $filters[] = "o.operation_type = ?";
            $params[] = sanitizeInput($_GET['operation_type']);
        }
        
        // تصفية حسب التاريخ
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $filters[] = "DATE(o.created_at) >= ?";
            $params[] = sanitizeInput($_GET['date_from']);
        }
        
        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $filters[] = "DATE(o.created_at) <= ?";
            $params[] = sanitizeInput($_GET['date_to']);
        }
        
        // بناء استعلام قائمة العمليات
        $operationsQuery = "SELECT o.*, d.name as driver_name, d.vehicle_number, m.name as material_name, c.name as camp_name 
                           FROM operations o 
                           LEFT JOIN drivers d ON o.driver_id = d.id 
                           LEFT JOIN materials m ON o.material_id = m.id 
                           LEFT JOIN camps c ON o.camp_id = c.id";
        
        if (!empty($filters)) {
            $operationsQuery .= " WHERE " . implode(" AND ", $filters);
        }
        
        // ترتيب النتائج
        $operationsQuery .= " ORDER BY o.created_at DESC";
        
        // تنفيذ الاستعلام
        $operations = fetchAll($operationsQuery, $params);
        
        // الحصول على قائمة الكمبات للتصفية
        if (hasPermission('manage_companies')) {
            $campsQuery = "SELECT id, name FROM camps ORDER BY name";
            $camps = fetchAll($campsQuery);
        }
        
        // الحصول على قائمة السائقين للتصفية
        $driversQuery = "SELECT id, name, vehicle_number FROM drivers";
        if (!hasPermission('manage_companies')) {
            $driversQuery .= " WHERE camp_id = ?";
            $drivers = fetchAll($driversQuery, [$_SESSION['camp_id']]);
        } else {
            $drivers = fetchAll($driversQuery);
        }
        
        // الحصول على قائمة المواد للتصفية
        $materialsQuery = "SELECT id, name FROM materials ORDER BY name";
        $materials = fetchAll($materialsQuery);
        
        include 'templates/operations/list.php';
        break;
}

/**
 * دالة للحصول على اسم السائق
 * @param int $driverId معرف السائق
 * @return string اسم السائق
 */
function getDriverName($driverId) {
    $driverQuery = "SELECT name FROM drivers WHERE id = ?";
    $driver = fetchRow($driverQuery, [$driverId]);
    return $driver ? $driver['name'] : 'غير معروف';
}
?>