<?php

/**
 * صفحة تسجيل الدخول
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// إذا كان المستخدم مسجل الدخول بالفعل، يتم توجيهه إلى الصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$error = '';

// معالجة نموذج تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // التحقق من بيانات المستخدم
        $sql = "SELECT * FROM users WHERE username = ?";
        $user = fetchRow($sql, [$username]);

        if ($user && password_verify($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['camp_id'] = $user['camp_id'];

            // تسجيل عملية تسجيل الدخول
            $logData = [
                'user_id' => $user['id'],
                'action' => 'login',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            insertData('activity_logs', $logData);

            header('Location: index.php');
            exit;
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Material Icons من Google -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome للأيقونات الإضافية -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- خطوط Google -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- ملف CSS المخصص -->
    <link rel="stylesheet" href="assets/css/style.css">


</head>

<body class="login-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-logo">
                        <div class="logo-icon">
                            <i class="material-icons">local_shipping</i>
                        </div>
                        <h3><?php echo SITE_NAME; ?></h3>
                        <p>نظام إدارة عمليات دخول وخروج سيارات الحمل</p>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <i class="material-icons me-2" style="vertical-align: middle;">error</i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="" id="loginForm">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                            <label for="username">
                                <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">person</i>
                                اسم المستخدم
                            </label>
                        </div>

                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                            <label for="password">
                                <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">lock</i>
                                كلمة المرور
                            </label>
                        </div>

                        <button type="submit" class="btn btn-login">
                            <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">login</i>
                            تسجيل الدخول
                        </button>
                    </form>

                    <div class="footer-text">
                        <div class="mb-2">
                            <strong>بيانات الدخول الافتراضية:</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>المستخدم: <code>admin</code></span>
                            <span>كلمة المرور: <code>admin123</code></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>