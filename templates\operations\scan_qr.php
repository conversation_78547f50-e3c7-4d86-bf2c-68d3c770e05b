<?php
/**
 * قالب مسح QR Code للسائق
 */
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3"><i class="fas fa-qrcode me-2"></i>مسح QR Code</h2>
        <a href="index.php?page=operations" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة إلى قائمة العمليات
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-camera me-2"></i>مسح QR Code للسائق</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <p class="lead">قم بتوجيه كاميرا الهاتف إلى رمز QR الخاص بالسائق</p>
                        <div id="qr-reader" class="mx-auto" style="width: 100%; max-width: 400px;"></div>
                    </div>
                    
                    <div class="text-center mb-3">
                        <p>أو</p>
                    </div>
                    
                    <form method="post" action="index.php?page=operations&action=process_qr" id="qr-form">
                        <div class="mb-3">
                            <label for="qr_data" class="form-label">أدخل بيانات QR Code يدويًا</label>
                            <input type="text" name="qr_data" id="qr_data" class="form-control" placeholder="أدخل بيانات QR Code">
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>معالجة البيانات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين مكتبة مسح QR Code -->
<script src="https://unpkg.com/html5-qrcode@2.3.4/html5-qrcode.min.js"></script>

<script>
// تهيئة قارئ QR Code
document.addEventListener('DOMContentLoaded', function() {
    const html5QrCode = new Html5Qrcode("qr-reader");
    const qrForm = document.getElementById('qr-form');
    const qrDataInput = document.getElementById('qr_data');
    
    const qrCodeSuccessCallback = (decodedText, decodedResult) => {
        // إيقاف المسح بعد العثور على رمز QR
        html5QrCode.stop();
        
        // تعيين قيمة النموذج وإرساله
        qrDataInput.value = decodedText;
        qrForm.submit();
    };
    
    const config = { fps: 10, qrbox: { width: 250, height: 250 } };
    
    // بدء المسح
    html5QrCode.start(
        { facingMode: "environment" },
        config,
        qrCodeSuccessCallback
    ).catch(error => {
        console.error("خطأ في بدء مسح QR Code:", error);
        alert("لا يمكن الوصول إلى الكاميرا. يرجى التحقق من إذن الكاميرا أو إدخال البيانات يدويًا.");
    });
});
</script>